# -*- coding: utf-8 -*-

import json
import logging
import time
from datetime import datetime, timedelta
from odoo import http
from odoo.http import request, Response
from odoo.exceptions import AccessError, UserError

_logger = logging.getLogger(__name__)

class SbotchatController(http.Controller):
    """SBotChat Controller - AI Chatbot for Odoo"""
    
    @http.route(['/sbotchat/chat', '/sbotchat/chat_with_openai'], type='json', auth='user', methods=['POST'])
    def chat(self, message=None, conversation_id=None, **kwargs):
        """Main chat endpoint"""
        try:
            if not message:
                return {'error': 'Message is required'}
            
            # Get configuration
            config = request.env['sbotchat.config'].get_active_config()
            if not config:
                return {'error': 'No active configuration found'}
            
            # Process message
            result = self._process_message(message, conversation_id, **kwargs)
            return result
                
        except Exception as e:
            _logger.error(f"Error in chat endpoint: {str(e)}")
            return {'error': f'Chat processing failed: {str(e)}'}
    
    def _process_message(self, message, conversation_id=None, **kwargs):
        """Process message with AI"""
        try:
            # Get or create conversation
            conversation = self._get_or_create_conversation(conversation_id)
            
            # Store user message
            request.env['sbotchat.message'].add_message(conversation.id, 'user', message)
            
            # Generate AI response
            ai_response = self._generate_ai_response(message, conversation, **kwargs)
            
            # Store AI response
            request.env['sbotchat.message'].add_message(conversation.id, 'assistant', ai_response)
            
            return {
                'success': True,
                'response': ai_response,
                'conversation_id': conversation.id,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            _logger.error(f"Error processing message: {str(e)}")
            return {'error': f'Message processing failed: {str(e)}'}
    
    def _get_or_create_conversation(self, conversation_id=None):
        """Get or create conversation"""
        if conversation_id:
            conversation = request.env['sbotchat.conversation'].browse(conversation_id)
            if conversation.exists():
                return conversation
        
        # Create new conversation
        conversation = request.env['sbotchat.conversation'].create({
            'title': f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            'user_id': request.env.user.id,
            'start_date': datetime.now()
        })
        return conversation
    
    def _generate_ai_response(self, message, conversation, **kwargs):
        """Generate AI response using OpenAI API with full Odoo 18 compatibility and Redis cache"""
        try:
            # Get configuration
            config = request.env['sbotchat.config'].get_active_config()
            
            # Enhanced debug logging
            _logger.info(f"Chat request - Config found: {bool(config)}")
            if config:
                _logger.info(f"Chat request - Config ID: {config.id}")
                _logger.info(f"Chat request - API Key exists: {bool(config.api_key)}")
                _logger.info(f"Chat request - API Key length: {len(config.api_key) if config.api_key else 0}")
                if config.api_key:
                    _logger.info(f"Chat request - API Key preview: {config.api_key[:10] + '...'}")
                else:
                    _logger.warning("Chat request - API Key is empty or None")
            
            if not config:
                _logger.error("Chat request failed: No configuration found")
                return "Lỗi: Không tìm thấy cấu hình. Vui lòng liên hệ quản trị viên để tạo cấu hình mặc định."
            
            if not config.api_key:
                _logger.error(f"Chat request failed: API key empty for config {config.id}")
                return "Lỗi: Chưa cấu hình API key OpenAI. Vui lòng vào Settings để nhập API key của bạn."
            
            # Get conversation context with Odoo 18 compatibility
            messages = self._build_context_messages(message, conversation, config)
            
            # 🚀 PHASE 1: Check Redis cache first
            cache_service = request.env['sbotchat.cache.service']
            cached_response = cache_service.get_cached_response(message, messages, config)
            
            if cached_response:
                _logger.info("🎯 Cache HIT - Returning cached response")
                return cached_response['response']
            
            # Call OpenAI API with error handling
            try:
                import openai
                client = openai.OpenAI(api_key=config.api_key)
                
                # Enhanced API call with Odoo 18 user context and timeout
                import time
                max_retries = getattr(config, 'max_retries', 3)
                retry_delay = getattr(config, 'retry_delay', 1.0)
                
                # Filter out invalid parameters for OpenAI API
                openai_valid_params = [
                    'stream', 'stop', 'logit_bias', 'logprobs', 'top_logprobs', 
                    'user', 'seed', 'tools', 'tool_choice', 'response_format'
                ]
                filtered_kwargs = {k: v for k, v in kwargs.items() if k in openai_valid_params}
                
                # Log parameters for debugging
                _logger.info(f"Original kwargs: {list(kwargs.keys())}")
                _logger.info(f"Filtered kwargs: {list(filtered_kwargs.keys())}")
                if 'enable_web_search' in kwargs:
                    _logger.info(f"Web search enabled: {kwargs['enable_web_search']}")
                
                for attempt in range(max_retries):
                    try:
                        _logger.info(f"OpenAI API call attempt {attempt + 1}/{max_retries}")
                        response = client.chat.completions.create(
                            model=config.model_type or "gpt-4o-mini",
                            messages=messages,
                            temperature=float(config.temperature or 0.7),
                            max_tokens=int(config.max_tokens or 1000),
                            top_p=1.0,
                            frequency_penalty=0.0,
                            presence_penalty=0.0,
                            timeout=30,  # 30 seconds timeout
                            **filtered_kwargs  # Use filtered parameters only
                        )
                        break  # Success, exit retry loop
                    except Exception as retry_error:
                        if attempt < max_retries - 1:  # Not the last attempt
                            _logger.warning(f"API call attempt {attempt + 1} failed: {str(retry_error)}")
                            time.sleep(retry_delay)
                            continue
                        else:  # Last attempt failed
                            raise retry_error
                
                ai_response = response.choices[0].message.content
                
                # 🚀 PHASE 1: Cache the response and check token warnings
                try:
                    tokens_used = response.usage.total_tokens if hasattr(response, 'usage') and response.usage else 0
                    cache_service.cache_response(message, messages, config, ai_response, tokens_used)
                    _logger.info(f"💾 Response cached - Tokens used: {tokens_used}")
                    
                    # 🚀 TOKEN WARNING SYSTEM: Check for token usage warnings
                    token_warning = cache_service.check_token_usage_warning(tokens_used, config)
                    if token_warning and token_warning.get('warning_level') != 'none':
                        warning_message = token_warning.get('message', '')
                        _logger.info(f"📊 Token usage warning: {warning_message}")
                        
                        # Add warning to response for critical/high levels
                        if token_warning.get('warning_level') in ['critical', 'high']:
                            ai_response += f"\n\n⚠️ {warning_message}"
                            
                except Exception as cache_error:
                    _logger.warning(f"⚠️ Failed to cache response or check token warnings: {str(cache_error)}")
                
                # Log successful response for monitoring
                _logger.info(f"AI response generated successfully for user {request.env.user.name}")
                
                return ai_response
                
            except Exception as api_error:
                error_msg = str(api_error)
                _logger.error(f"OpenAI API error details: {error_msg}")
                _logger.error(f"Error type: {type(api_error).__name__}")
                _logger.error(f"Error args: {api_error.args}")
                
                # Enhanced error handling with specific cases
                if "unexpected keyword argument" in error_msg.lower():
                    _logger.error("Parameter validation failed - unexpected keyword argument detected")
                    return "Lỗi cấu hình tham số. Hệ thống đã được cập nhật để khắc phục."
                elif "authentication" in error_msg.lower():
                    return "Lỗi xác thực OpenAI API. Vui lòng kiểm tra API key."
                elif "rate_limit" in error_msg.lower():
                    return "Hệ thống đang bận, vui lòng thử lại sau ít phút."
                elif "quota" in error_msg.lower():
                    return "Đã hết quota API. Vui lòng liên hệ quản trị viên."
                elif "timeout" in error_msg.lower():
                    return "Kết nối bị timeout. Vui lòng thử lại."
                elif "connection" in error_msg.lower():
                    return "Lỗi kết nối mạng. Vui lòng kiểm tra internet."
                elif "model" in error_msg.lower() and "not found" in error_msg.lower():
                    return "Model AI không tồn tại. Vui lòng kiểm tra cấu hình model."
                else:
                    _logger.error(f"Unhandled OpenAI API error: {error_msg}")
                    return "Đã xảy ra lỗi không mong muốn. Vui lòng thử lại hoặc liên hệ hỗ trợ."
            
        except Exception as e:
            _logger.error(f"Error in _generate_ai_response: {str(e)}")
            return "Xin lỗi, tôi gặp lỗi khi xử lý yêu cầu của bạn. Vui lòng thử lại."

    def _build_context_messages(self, message, conversation, config):
        """Build context messages for OpenAI API with Odoo 18 compatibility"""
        try:
            messages = []
            
            # Add system prompt with Odoo 18 user context
            system_prompt = self._build_system_prompt(config)
            messages.append({
                "role": "system",
                "content": system_prompt
            })
            
            # Add conversation history (last 10 messages for optimal performance)
            history = self._get_conversation_history(conversation, limit=getattr(config, 'context_window_size', 10))
            messages.extend(history)
            
            # Add current user message
            messages.append({
                "role": "user",
                "content": message
            })
            
            return messages
            
        except Exception as e:
            _logger.error(f"Error building context messages: {e}")
            # Fallback to simple context
            return [
                {"role": "system", "content": "Bạn là trợ lý AI thông minh và hữu ích cho hệ thống Odoo."},
                {"role": "user", "content": message}
            ]

    def _build_system_prompt(self, config):
        """Build intelligent system prompt with Odoo 18 context"""
        try:
            # Enhanced intelligent system prompt
            base_prompt = config.system_prompt or """Bạn là SBot - trợ lý AI thông minh và chuyên nghiệp cho hệ thống quản lý doanh nghiệp Odoo. 

🎯 SỨ MỆNH: Hỗ trợ người dùng hiệu quả với mọi câu hỏi và yêu cầu về quản lý kinh doanh.

💡 KHẢ NĂNG CHÍNH:
- Phân tích và giải quyết vấn đề kinh doanh
- Tư vấn quy trình tối ưu cho doanh nghiệp  
- Hướng dẫn chi tiết sử dụng Odoo
- Giải thích khái niệm phức tạp một cách đơn giản
- Đưa ra giải pháp sáng tạo và thực tế"""
            
            # Add Odoo 18 and user context
            user = request.env.user
            company = user.company_id
            
            context_info = f"""

📊 THÔNG TIN PHIÊN LÀM VIỆC:
• Hệ thống: Odoo 18 Enterprise
• Người dùng: {user.name}
• Email: {user.email}  
• Công ty: {company.name if company else 'N/A'}
• Múi giờ: {user.tz or 'UTC'}

🤖 CÁCH THỨC HOẠT ĐỘNG:
✅ Luôn trả lời THÔNG MINH, CHI TIẾT và HỮU ÍCH
✅ Sử dụng tiếng Việt tự nhiên và chuyên nghiệp
✅ Cung cấp ví dụ cụ thể khi có thể
✅ Giải thích từng bước một cách rõ ràng
✅ Đặt câu hỏi làm rõ khi cần thiết
✅ Đưa ra nhiều góc nhìn và giải pháp
✅ Tập trung vào giá trị thực tế cho người dùng

🚀 PHONG CÁCH:
• Thân thiện nhưng chuyên nghiệp
• Sáng tạo và linh hoạt  
• Tích cực và hỗ trợ tối đa
• Chính xác về mặt kỹ thuật
            """
            
            return base_prompt + context_info
            
        except Exception as e:
            _logger.error(f"Error building system prompt: {e}")
            return "Bạn là trợ lý AI thông minh cho hệ thống Odoo. Hãy trả lời bằng tiếng Việt và hỗ trợ người dùng một cách chuyên nghiệp."

    def _get_conversation_history(self, conversation, limit=10):
        """Get conversation history for context with Odoo 18 compatibility"""
        try:
            history = []
            if not conversation or not conversation.message_ids:
                return history
                
            # Get recent messages sorted by creation date
            messages = conversation.message_ids.sorted('create_date', reverse=True)[:limit]
            
            # Build history in chronological order
            for msg in reversed(messages):
                try:
                    role = "assistant" if msg.role == "assistant" else "user"
                    content = msg.content or ""
                    
                    # Ensure content is not empty and is string
                    if content and isinstance(content, str) and len(content.strip()) > 0:
                        history.append({
                            "role": role,
                            "content": content.strip()
                        })
                except Exception as msg_error:
                    _logger.warning(f"Skipping message due to error: {msg_error}")
                    continue
            
            return history
            
        except Exception as e:
            _logger.error(f"Error getting conversation history: {e}")
            return []
    
    @http.route('/sbotchat/conversations', type='json', auth='user', methods=['GET', 'POST'])
    def get_conversations(self, **kwargs):
        """Get user conversations"""
        try:
            conversations = request.env['sbotchat.conversation'].search([
                ('user_id', '=', request.env.user.id)
            ], order='create_date desc', limit=20)
            
            return {
                'success': True,
                'conversations': [{
                    'id': conv.id,
                    'name': conv.name,
                    'start_date': conv.start_date.isoformat() if conv.start_date else None,
                    'message_count': len(conv.message_ids)
                } for conv in conversations]
            }
            
        except Exception as e:
            _logger.error(f"Error getting conversations: {str(e)}")
            return {'error': f'Failed to get conversations: {str(e)}'}
    
    @http.route('/sbotchat/conversation/<int:conversation_id>/messages', type='json', auth='user', methods=['GET'])
    def get_conversation_messages(self, conversation_id, **kwargs):
        """Get conversation messages"""
        try:
            conversation = request.env['sbotchat.conversation'].browse(conversation_id)
            if not conversation.exists():
                return {'error': 'Conversation not found'}
            
            if conversation.user_id.id != request.env.user.id:
                return {'error': 'Access denied'}
            
            messages = conversation.message_ids.sorted('create_date')
            
            return {
                'success': True,
                'messages': [{
                        'id': msg.id,
                        'role': msg.role,
                    'content': msg.content,
                    'create_date': msg.create_date.isoformat() if msg.create_date else None
                } for msg in messages]
            }
            
        except Exception as e:
            _logger.error(f"Error getting conversation messages: {str(e)}")
            return {'error': f'Failed to get messages: {str(e)}'}
    
    @http.route('/sbotchat/config', type='json', auth='user', methods=['POST'])
    def get_config(self, **kwargs):
        """Get or update configuration based on data presence"""
        try:
            # Determine if this is an update request based on presence of update fields
            update_fields = ['api_key', 'model_type', 'temperature', 'max_tokens', 'system_prompt', 
                           'enable_web_search', 'enable_vector_store', 'is_active', 'rate_limit_per_hour', 
                           'allow_delete', 'brave_search_api_key']
            
            has_update_data = any(field in kwargs and kwargs[field] is not None for field in update_fields)
            
            _logger.info(f"Config request - Has update data: {has_update_data}, Fields: {list(kwargs.keys())}")
            
            if has_update_data:
                # Handle update configuration
                _logger.info("Processing config UPDATE request")
                return self._update_config(**kwargs)
            else:
                # Handle get current configuration
                _logger.info("Processing config GET request")
                return self._get_config()
                
        except Exception as e:
            _logger.error(f"Error in config endpoint: {str(e)}")
            return {'error': f'Failed to process config: {str(e)}'}
    
    def _get_config(self):
        """Get current configuration with detailed logging"""
        try:
            config = request.env['sbotchat.config'].get_active_config()
            if not config:
                _logger.error("No configuration found for get request")
                return {'error': 'No configuration found'}
            
            _logger.info(f"Retrieved config {config.id} - API key exists: {bool(config.api_key)}, length: {len(config.api_key) if config.api_key else 0}")
            
            return {
                'success': True,
                'message': 'Configuration retrieved successfully',
                'config_id': config.id,
                'data': {
                    'id': config.id,
                    'name': config.name,
                    'model_type': config.model_type,
                    'api_key_configured': bool(config.api_key),
                    'is_active': config.is_active,
                    'api_key': config.api_key if config.api_key else '',
                    'temperature': config.temperature,
                    'max_tokens': config.max_tokens,
                    'system_prompt': config.system_prompt,
                    'enable_web_search': config.enable_web_search,
                    'enable_vector_store': config.enable_vector_store,
                    'rate_limit_per_hour': config.rate_limit_per_hour,
                    'allow_delete': config.allow_delete,
                    'brave_search_api_key': config.brave_search_api_key if hasattr(config, 'brave_search_api_key') else ''
                }
            }
            
        except Exception as e:
            _logger.error(f"Error getting config: {str(e)}", exc_info=True)
            return {'error': f'Failed to get config: {str(e)}'}
    
    def _update_config(self, **kwargs):
        """Update configuration with enhanced validation"""
        try:
            config = request.env['sbotchat.config'].get_active_config()
            if not config:
                _logger.error("No configuration found for update")
                return {'error': 'No configuration found'}
            
            # Update fields from kwargs with validation
            update_data = {}
            
            if 'api_key' in kwargs and kwargs['api_key']:
                api_key = str(kwargs['api_key']).strip()
                if api_key and api_key != '••••••••':
                    update_data['api_key'] = api_key
                    _logger.info(f"Updating API key for config {config.id} (length: {len(api_key)})")
                else:
                    _logger.warning("Empty or masked API key provided, skipping update")
            
            if 'brave_search_api_key' in kwargs:
                update_data['brave_search_api_key'] = kwargs['brave_search_api_key']
            
            if 'model_type' in kwargs:
                update_data['model_type'] = kwargs['model_type']
            
            if 'temperature' in kwargs:
                try:
                    update_data['temperature'] = float(kwargs['temperature'])
                except (ValueError, TypeError):
                    _logger.warning(f"Invalid temperature value: {kwargs['temperature']}")
            
            if 'max_tokens' in kwargs:
                try:
                    update_data['max_tokens'] = int(kwargs['max_tokens'])
                except (ValueError, TypeError):
                    _logger.warning(f"Invalid max_tokens value: {kwargs['max_tokens']}")
            
            if 'system_prompt' in kwargs:
                update_data['system_prompt'] = kwargs['system_prompt']
            
            if 'enable_web_search' in kwargs:
                update_data['enable_web_search'] = bool(kwargs['enable_web_search'])
            
            if 'enable_vector_store' in kwargs:
                update_data['enable_vector_store'] = bool(kwargs['enable_vector_store'])
            
            if 'is_active' in kwargs:
                update_data['is_active'] = bool(kwargs['is_active'])
                
            if 'rate_limit_per_hour' in kwargs:
                try:
                    update_data['rate_limit_per_hour'] = int(kwargs['rate_limit_per_hour'])
                except (ValueError, TypeError):
                    _logger.warning(f"Invalid rate_limit_per_hour value: {kwargs['rate_limit_per_hour']}")
            
            if 'allow_delete' in kwargs:
                update_data['allow_delete'] = bool(kwargs['allow_delete'])
            
            # Update configuration
            if update_data:
                config.write(update_data)
                _logger.info(f"Configuration {config.id} updated successfully with fields: {list(update_data.keys())}")
                
                # Log API key status after update
                config_after_update = request.env['sbotchat.config'].get_active_config()
                _logger.info(f"After update - API key exists: {bool(config_after_update.api_key)}, length: {len(config_after_update.api_key) if config_after_update.api_key else 0}")
                
                return {
                    'success': True,
                    'message': 'Configuration updated successfully',
                    'config_id': config.id,
                    'updated_fields': list(update_data.keys()),
                    'data': {
                        'api_key_configured': bool(config_after_update.api_key),
                        'model_type': config_after_update.model_type,
                        'temperature': config_after_update.temperature,
                        'max_tokens': config_after_update.max_tokens
                    }
                }
            else:
                _logger.warning("No valid update data provided")
                return {'error': 'No valid update data provided'}
            
        except Exception as e:
            _logger.error(f"Error updating config: {str(e)}", exc_info=True)
            return {'error': f'Failed to update config: {str(e)}'}
    
    @http.route('/sbotchat/health', type='json', auth='user', methods=['GET', 'POST'])
    def health_check(self, **kwargs):
        """Health check endpoint"""
        try:
            config = request.env['sbotchat.config'].get_active_config()
            
            return {
                'success': True,
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'config_active': bool(config),
                'user_id': request.env.user.id
            }
            
        except Exception as e:
            _logger.error(f"Health check failed: {str(e)}")
            return {
                'success': False,
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    @http.route('/sbotchat/notifications', type='json', auth='user', methods=['GET', 'POST'])
    def get_notifications(self, unread_only=False, **kwargs):
        """Get user notifications"""
        try:
            # Get notifications from bus or create mock data
            notifications = []
            
            # Mock notifications for now
            notifications = [
                {
                    'id': 1,
                    'title': 'Welcome to SBotChat',
                    'message': 'Your AI assistant is ready to help!',
                    'type': 'info',
                    'timestamp': datetime.now().isoformat(),
                    'read': False
                }
            ]
            
            return {
                'success': True,
                'notifications': notifications,
                'unread_count': len([n for n in notifications if not n.get('read', False)])
            }
            
        except Exception as e:
            _logger.error(f"Error getting notifications: {str(e)}")
            return {'error': f'Failed to get notifications: {str(e)}'}
    
    @http.route('/sbotchat/dashboard', type='json', auth='user', methods=['GET', 'POST'])
    def get_dashboard_data(self, **kwargs):
        """Get dashboard data"""
        try:
            # Mock dashboard data
            dashboard_data = {
                'attendance': {
                    'present': 85,
                    'absent': 15,
                    'late': 5
                },
                'leaves': {
                    'pending': 3,
                    'approved': 12,
                    'rejected': 1
                },
                'payroll': {
                    'total_employees': 50,
                    'total_salary': 250000000,
                    'average_salary': 5000000
                }
            }
            
            return {
                'success': True,
                'data': dashboard_data,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            _logger.error(f"Error getting dashboard data: {str(e)}")
            return {'error': f'Failed to get dashboard data: {str(e)}'}
    
    @http.route('/sbotchat/quick-action', type='json', auth='user', methods=['POST'])
    def execute_quick_action(self, action_type=None, action_id=None, **kwargs):
        """Execute quick action"""
        try:
            if not action_type or not action_id:
                return {'error': 'Action type and ID are required'}
            
            # Mock quick action execution
            result = {
                    'success': True,
                'message': f'Quick action {action_type} executed successfully',
                'action_id': action_id,
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            _logger.error(f"Error executing quick action: {str(e)}")
            return {'error': f'Failed to execute quick action: {str(e)}'}
    
    @http.route('/sbotchat/conversations', type='json', auth='user', methods=['GET'])
    def get_conversations_list(self, **kwargs):
        """Get user conversations list"""
        return self.get_conversations(**kwargs)
    
    @http.route('/sbotchat/get_conversation_messages', type='json', auth='user', methods=['GET'])
    def get_conversation_messages_api(self, conversation_id=None, **kwargs):
        """Get conversation messages API"""
        if not conversation_id:
            return {'error': 'Conversation ID is required'}
        return self.get_conversation_messages(conversation_id, **kwargs)
    
    @http.route('/sbotchat/create_conversation', type='json', auth='user', methods=['POST'])
    def create_conversation(self, name=None, **kwargs):
        """Create new conversation"""
        try:
            if not name:
                name = f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            
            conversation = request.env['sbotchat.conversation'].create({
                'name': name,
                        'user_id': request.env.user.id,
                'start_date': datetime.now()
                    })

            return {
                'success': True,
                'conversation_id': conversation.id,
                'name': conversation.name
            }

        except Exception as e:
            _logger.error(f"Error creating conversation: {str(e)}")
            return {'error': f'Failed to create conversation: {str(e)}'}
    
    @http.route('/sbotchat/send_message', type='json', auth='user', methods=['POST'])
    def send_message(self, **kwargs):
        """Send message API - alias for chat"""
        return self.chat(**kwargs)
    
    @http.route('/sbotchat/conversation_messages', type='json', auth='user', methods=['GET'])
    def conversation_messages(self, conversation_id=None, **kwargs):
        """Get conversation messages - alias"""
        if not conversation_id:
            return {'error': 'Conversation ID is required'}
        return self.get_conversation_messages(conversation_id, **kwargs)
    
    @http.route('/sbotchat/rename_conversation', type='json', auth='user', methods=['POST'])
    def rename_conversation(self, conversation_id=None, new_name=None, **kwargs):
        """Rename conversation"""
        try:
            if not conversation_id or not new_name:
                return {'error': 'Conversation ID and new name are required'}
            
            conversation = request.env['sbotchat.conversation'].browse(conversation_id)
            if not conversation.exists():
                return {'error': 'Conversation not found'}
            
            if conversation.user_id.id != request.env.user.id:
                return {'error': 'Access denied'}
            
            conversation.write({'name': new_name})
            
            return {
                'success': True, 
                'message': 'Conversation renamed successfully'
            }
            
        except Exception as e:
            _logger.error(f"Error renaming conversation: {str(e)}")
            return {'error': f'Failed to rename conversation: {str(e)}'}

    @http.route('/sbotchat/delete_conversation', type='json', auth='user', methods=['POST'])
    def delete_conversation(self, conversation_id=None, **kwargs):
        """Delete conversation"""
        try:
            if not conversation_id:
                return {'error': 'Conversation ID is required'}
            
            conversation = request.env['sbotchat.conversation'].browse(conversation_id)
            if not conversation.exists():
                return {'error': 'Conversation not found'}
            
            if conversation.user_id.id != request.env.user.id:
                return {'error': 'Access denied'}
            
            conversation.unlink()
            
            return {
                'success': True, 
                'message': 'Conversation deleted successfully'
            }
            
        except Exception as e:
            _logger.error(f"Error deleting conversation: {str(e)}")
            return {'error': f'Failed to delete conversation: {str(e)}'}
    
    @http.route('/sbotchat/duplicate_conversation', type='json', auth='user', methods=['POST'])
    def duplicate_conversation(self, conversation_id=None, **kwargs):
        """Duplicate conversation"""
        try:
            if not conversation_id:
                return {'error': 'Conversation ID is required'}
            
            original = request.env['sbotchat.conversation'].browse(conversation_id)
            if not original.exists():
                return {'error': 'Conversation not found'}
            
            if original.user_id.id != request.env.user.id:
                return {'error': 'Access denied'}
            
            # Create duplicate
            duplicate = original.copy({
                'name': f"{original.name} (Copy)",
                'start_date': datetime.now()
            })

            return {
                'success': True,
                'conversation_id': duplicate.id,
                'message': 'Conversation duplicated successfully'
            }
            
        except Exception as e:
            _logger.error(f"Error duplicating conversation: {str(e)}")
            return {'error': f'Failed to duplicate conversation: {str(e)}'}
    
    # Dashboard routes
    @http.route('/sbotchat/dashboard/realtime_stats', type='json', auth='user', methods=['GET'])
    def get_realtime_stats(self, **kwargs):
        """Get realtime dashboard stats"""
        try:
            # Mock realtime stats
            stats = {
                'online_users': 25,
                'active_conversations': 8,
                'messages_today': 156,
                'avg_response_time': '2.3s',
                'system_load': 45
            }
            
            return {
                'success': True,
                'stats': stats,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            _logger.error(f"Error getting realtime stats: {str(e)}")
            return {'error': f'Failed to get realtime stats: {str(e)}'}
    
    @http.route('/sbotchat/dashboard/critical_updates', type='json', auth='user', methods=['GET'])
    def get_critical_updates(self, **kwargs):
        """Get critical updates"""
        try:
            # Mock critical updates
            updates = [
                {
                    'id': 1,
                    'type': 'warning',
                    'title': 'System Maintenance',
                    'message': 'Scheduled maintenance at 2 AM',
                    'timestamp': datetime.now().isoformat()
                }
            ]
            
            return {
                'success': True,
                'updates': updates
            }
        
        except Exception as e:
            _logger.error(f"Error getting critical updates: {str(e)}")
            return {'error': f'Failed to get critical updates: {str(e)}'}
    
    # Notification routes
    @http.route(['/sbotchat/notification/<int:notification_id>/mark_read'], type='json', auth='user', methods=['POST'])
    def mark_notification_read(self, notification_id, **kwargs):
        """Mark notification as read"""
        try:
            # Mock marking as read
            return {
                'success': True,
                'message': f'Notification {notification_id} marked as read'
            }
            
        except Exception as e:
            _logger.error(f"Error marking notification read: {str(e)}")
            return {'error': f'Failed to mark notification read: {str(e)}'}
    
    @http.route('/sbotchat/notification/test', type='json', auth='user', methods=['GET'])
    def test_notification(self, **kwargs):
        """Test notification"""
        try:
            return {
                'success': True,
                'message': 'Notification test successful',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            _logger.error(f"Error testing notification: {str(e)}")
            return {'error': f'Failed to test notification: {str(e)}'} 