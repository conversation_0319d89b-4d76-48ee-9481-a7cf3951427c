# -*- coding: utf-8 -*-

from odoo import models, fields, api
import logging
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)

class SbotchatConfig(models.Model):
    _name = 'sbotchat.config'
    _description = '<PERSON><PERSON>u hình SBot Chat'
    _rec_name = 'name'

    name = fields.Char('Tên cấu hình', required=True, default='Cấu hình mặc định')
    api_key = fields.Char('Khóa API OpenAI', help='Khóa API OpenAI của bạn từ platform.openai.com')
    model_type = fields.Selection([
        ('gpt-4o-mini', 'GPT-4o Mini (Khuyến nghị)'),
        ('gpt-4o', 'GPT-4o (<PERSON> cấp)'),
        ('gpt-4-turbo', 'GPT-4 Turbo')
    ], string='Loại mô hình', default='gpt-4o-mini', required=True, 
       help='Chọn mô hình OpenAI phù hợp: GPT-4o Mini (nhanh, rẻ), GPT-4o (cân bằng), GPT-4 Turbo (mạnh nhất)')
    
    # 🚀 OPTIMIZED Model parameters for faster performance (<15s)
    temperature = fields.Float('Nhiệt độ', default=0.1, help='Kiểm soát tính ngẫu nhiên (0.0-2.0) - Giảm xuống 0.7 để nhanh hơn')
    max_tokens = fields.Integer('Số token tối đa', default=2000, help='Số token tối đa trong phản hồi (1-16384) - Giảm xuống 2000 để nhanh hơn')
    top_p = fields.Float('Top P', default=0.9, help='Tham số nucleus sampling (0.0-1.0) - Giảm xuống 0.9 để focus hơn')
    frequency_penalty = fields.Float('Phạt tần suất', default=0.1, help='Phạt các token thường xuyên (-2.0 đến 2.0)')
    presence_penalty = fields.Float('Phạt hiện diện', default=0.1, help='Phạt các token hiện tại (-2.0 đến 2.0)')
    rate_limit_per_hour = fields.Integer('Giới hạn request mỗi giờ', default=10000, help='Số tin nhắn tối đa gửi tới LLM mỗi giờ cho mỗi người dùng.')
    allow_delete = fields.Boolean('Cho phép xoá dữ liệu', default=False, help='Nếu được bật, chatbot có quyền gọi hàm delete_records để xoá bản ghi Odoo. Người dùng phải đồng ý điều khoản miễn trừ trách nhiệm.')
    
    # System settings
    system_prompt = fields.Text('Lời nhắc hệ thống', default='Bạn là một trợ lý AI hữu ích.',
                               help='Hướng dẫn cho hành vi của trợ lý AI')
    is_active = fields.Boolean('Hoạt động', default=True)
    user_id = fields.Many2one('res.users', string='Người dùng', default=lambda self: self.env.user, required=True)
    
    # 🚀 OPTIMIZED AI settings for faster execution (<15s)
    # Reduced memory window and max iterations for speed
    
    
    # 🚀 REDIS INFRASTRUCTURE CONFIGURATION - PHASE 1
    # Redis Cache Settings
    redis_enabled = fields.Boolean(
        string='Enable Redis Cache', 
        default=True,
        help='Enable Redis caching for 85% token reduction and 2x performance boost'
    )
    
    redis_host = fields.Char(
        string='Redis Host', 
        default='redis',
        help='Redis server hostname or IP address (use "redis" for Docker)'
    )
    
    redis_port = fields.Integer(
        string='Redis Port', 
        default=6379,
        help='Redis server port number'
    )
    
    redis_password = fields.Char(
        string='Redis Password',
        help='Redis server password (optional)'
    )
    
    redis_db = fields.Integer(
        string='Redis Database', 
        default=0,
        help='Redis database number (0-15)'
    )
    
    redis_timeout = fields.Integer(
        string='Redis Timeout (seconds)', 
        default=5,
        help='Connection timeout for Redis operations'
    )
    
    redis_max_connections = fields.Integer(
        string='Redis Max Connections', 
        default=20,
        help='Maximum connections in Redis connection pool'
    )
    
    # Cache Configuration
    cache_responses = fields.Boolean(
        string='Cache AI Responses', 
        default=True,
        help='Cache AI responses to reduce token usage by 85%'
    )
    
    cache_duration = fields.Integer(
        string='Cache Duration (hours)', 
        default=6,
        help='How long to keep cached responses (1-168 hours)'
    )
    
    cache_context = fields.Boolean(
        string='Cache Context', 
        default=True,
        help='Cache conversation context to reduce token usage'
    )
    
    cache_models = fields.Boolean(
        string='Cache Odoo Models', 
        default=True,
        help='Cache Odoo model schemas for faster access'
    )
    
    cache_user_preferences = fields.Boolean(
        string='Cache User Preferences', 
        default=True,
        help='Cache user preferences and settings'
    )
    
    # Web Search Configuration
    enable_web_search = fields.Boolean(
        string='Enable Web Search',
        default=True,
        help='Allow AI to search web for real-time information'
    )
    
    brave_search_api_key = fields.Char(
        string='Brave Search API Key',
        default='BSAPiMD7imYAMAuDRNw-EQC01UbHh_o',
        help='API key for Brave Search service'
    )
    
    # Vector Store Configuration  
    enable_vector_store = fields.Boolean(
        string='Enable Vector Store',
        default=True,
        help='Enable OpenAI Vector Store for document search and RAG'
    )
    
    default_vector_store_id = fields.Many2one(
        'sbotchat.vector.store',
        string='Default Vector Store',
        help='Default Vector Store to use for document search'
    )
    
    vector_search_enabled = fields.Boolean(
        string='Vector Search Default Enabled',
        default=False,
        help='Default state of vector search for new conversations'
    )
    
    vector_search_max_results = fields.Integer(
        string='Max Vector Search Results',
        default=5,
        help='Maximum number of vector search results to include in context'
    )
    
    vector_search_similarity_threshold = fields.Float(
        string='Similarity Threshold',
        default=0.7,
        help='Minimum similarity score for vector search results (0.0-1.0)'
    )
    
    vector_auto_search = fields.Boolean(
        string='Auto Vector Search',
        default=True,
        help='Automatically search vector store for relevant documents'
    )
    
    web_search_default_enabled = fields.Boolean(
        string='Web Search Default Enabled',
        default=False,
        help='Default state of web search toggle button for new conversations'
    )
    
    web_search_max_results = fields.Integer(
        string='Max Search Results',
        default=3,
        help='Maximum number of search results to return (1-10) - Giảm xuống 3 để nhanh hơn'
    )
    
    web_search_language = fields.Selection([
        ('vi', 'Tiếng Việt'),
        ('en', 'English'),
        ('auto', 'Auto-detect'),
    ], string='Search Language', default='vi',
       help='Preferred language for search results')
    
    web_search_safe_mode = fields.Selection([
        ('strict', 'Strict'),
        ('moderate', 'Moderate'), 
        ('off', 'Off'),
    ], string='Safe Search', default='moderate',
       help='Safe search filter level')
    
    # ✅ OPTIMIZED Token Management Settings for speed
    token_safety_enabled = fields.Boolean(
        string='Enable Token Safety Check',
        default=True,
        help='Automatically check and prevent token limit errors'
    )
    
    token_limit_threshold = fields.Integer(
        string='Token Limit Threshold',
        default=50000,
        help='Maximum tokens allowed per conversation - Giảm xuống 40k để nhanh hơn'
    )
    
    token_safety_percentage = fields.Float(
        string='Safety Threshold Percentage',
        default=75.0,
        help='Percentage of token limit to trigger optimization - Giảm xuống 75% để nhanh hơn'
    )
    
    auto_optimize_enabled = fields.Boolean(
        string='Auto-Optimize Long Conversations',
        default=True,
        help='Automatically optimize conversations when they approach token limit'
    )
    
    token_estimation_method = fields.Selection([
        ('conservative', 'Conservative (3 chars = 1 token)'),
        ('moderate', 'Moderate (3.5 chars = 1 token)'),
        ('aggressive', 'Aggressive (4 chars = 1 token)'),
    ], string='Token Estimation Method', default='aggressive',
       help='Method for estimating token count from text content - Đổi sang aggressive để ước lượng nhanh hơn')
    
    # Cấu hình rate limiting nâng cao
    rate_limit_requests = fields.Integer('Giới hạn requests', default=1000, 
                                        help='Số requests tối đa mỗi giờ (mặc định: 10000)')
    rate_limit_window = fields.Integer('Cửa sổ thời gian (giây)', default=7200,
                                      help='Thời gian reset rate limit (mặc định: 3600 giây = 1 giờ)')
    
    # 🚀 REDIS SHARED MEMORY CONFIGURATION
    redis_shared_memory_enabled = fields.Boolean(
        string='Enable Redis Shared Memory',
        default=True,
        help='Enable Redis for persistent shared memory across workers and server restarts'
    )
    
    redis_url = fields.Char(
        string='Redis Connection URL',
        default='redis://redis:6379',
        help='Redis server connection URL (default: redis://redis:6379 for Docker)'
    )
    
    redis_shared_db = fields.Integer(
        string='Redis Database Number',
        default=3,
        help='Redis database number for SBotChat (0-15, default: 3)'
    )
    
    redis_memory_ttl = fields.Integer(
        string='Memory TTL (days)',
        default=30,
        help='How long to keep conversation memory in Redis (days)'
    )
    
    redis_cache_ttl = fields.Integer(
        string='Cache TTL (hours)',
        default=6,
        help='How long to keep cache data in Redis (hours)'
    )
    
    redis_session_ttl = fields.Integer(
        string='Session TTL (hours)',
        default=24,
        help='How long to keep session data in Redis (hours)'
    )
    
    # ================================================
    # AI ENHANCEMENT FIELDS - ODOO 18 COMPATIBLE
    # ================================================
    
    # Context and Memory Management
    enable_context_memory = fields.Boolean(
        string='Bật Context Memory', 
        default=True, 
        help="Cho phép AI nhớ lịch sử cuộc hội thoại để trả lời thông minh hơn"
    )
    
    context_window_size = fields.Integer(
        string='Kích thước Context Window', 
        default=10,
        help="Số tin nhắn gần nhất được sử dụng làm context (1-50)"
    )
    
    enable_user_profiling = fields.Boolean(
        string='Bật User Profiling', 
        default=True,
        help="Cá nhân hóa phản hồi dựa trên thông tin người dùng Odoo"
    )
    
    response_optimization = fields.Boolean(
        string='Tối ưu hóa Response', 
        default=True,
        help="Tối ưu hóa chất lượng và độ chính xác của phản hồi AI"
    )
    
    # Advanced AI Features
    enable_streaming = fields.Boolean(
        string='Bật Streaming Responses', 
        default=False,
        help="Hiển thị phản hồi theo thời gian thực (cần hỗ trợ WebSocket)"
    )
    
    enable_function_calling = fields.Boolean(
        string='Bật Function Calling', 
        default=True,
        help="Cho phép AI gọi các function và API của Odoo"
    )
    
    enable_web_search_enhanced = fields.Boolean(
        string='Bật Web Search nâng cao', 
        default=False,
        help="Web search với AI analysis và context integration"
    )
    
    enable_file_analysis = fields.Boolean(
        string='Bật File Analysis', 
        default=False,
        help="Cho phép AI phân tích file đính kèm (PDF, Excel, etc.)"
    )
    
    # Error Handling and Reliability
    fallback_response = fields.Text(
        string='Phản hồi dự phòng', 
        default="Xin lỗi, tôi gặp lỗi khi xử lý yêu cầu. Vui lòng thử lại sau.",
        help="Tin nhắn hiển thị khi AI gặp lỗi hoặc không thể xử lý"
    )
    
    max_retries = fields.Integer(
        string='Số lần thử lại tối đa', 
        default=3,
        help="Số lần thử lại khi gọi OpenAI API thất bại (1-10)"
    )
    
    retry_delay = fields.Float(
        string='Độ trễ giữa các lần thử (giây)', 
        default=1.0,
        help="Thời gian chờ giữa các lần thử lại API (0.1-10.0 giây)"
    )
    
    # Performance and Caching
    cache_responses = fields.Boolean(
        string='Cache AI Responses', 
        default=True,
        help="Lưu cache phản hồi AI để tăng tốc độ và giảm API calls"
    )
    
    cache_duration = fields.Integer(
        string='Thời gian Cache (phút)', 
        default=60,
        help="Thời gian lưu cache phản hồi AI (5-1440 phút)"
    )
    
    response_timeout = fields.Integer(
        string='Timeout Response (giây)', 
        default=30,
        help="Thời gian chờ tối đa cho phản hồi AI (5-120 giây)"
    )
    
    # Odoo 18 Integration Settings
    odoo_integration_level = fields.Selection([
        ('basic', 'Cơ bản - Chỉ thông tin user'),
        ('standard', 'Tiêu chuẩn - Thông tin công ty và quyền'),
        ('advanced', 'Nâng cao - Full access to Odoo data'),
    ], string='Mức độ tích hợp Odoo', default='standard',
       help='Mức độ truy cập dữ liệu Odoo cho AI')
    
    enable_odoo_data_access = fields.Boolean(
        string='Cho phép AI truy cập dữ liệu Odoo', 
        default=True,
        help="Cho phép AI đọc và phân tích dữ liệu từ các module Odoo"
    )
    
    enable_odoo_actions = fields.Boolean(
        string='Cho phép AI thực hiện actions', 
        default=False,
        help="Cho phép AI tạo/sửa/xóa records trong Odoo (cần cẩn trọng)"
    )
    
    @api.model
    def get_active_config(self):
        """Get active configuration for current user"""
        try:
            config = self.search([
                ('user_id', '=', self.env.user.id),
                ('is_active', '=', True)
            ], limit=1)
            
            if not config:
                # Create default config if none exists
                _logger.info(f"Tạo cấu hình mặc định cho người dùng {self.env.user.login} (ID: {self.env.user.id})")
                config = self.create({
                    'name': f'Cấu hình mặc định - {self.env.user.login}',
                    'api_key': '',
                    'model_type': 'gpt-4o-mini',
                    'temperature': 0.7,
                    'max_tokens': 2000,
                    'top_p': 0.9,
                    'frequency_penalty': 0.1,
                    'presence_penalty': 0.1,
                    'system_prompt': 'Bạn là một trợ lý AI hữu ích.',
                    'user_id': self.env.user.id,
                    'is_active': True,
                })
                _logger.info(f"Đã tạo cấu hình mặc định với ID {config.id} cho người dùng {self.env.user.login}")
            else:
                _logger.info(f"Tìm thấy cấu hình hiện có ID {config.id} cho người dùng {self.env.user.login}")
            
            return config
        except Exception as e:
            _logger.error(f"Lỗi khi lấy cấu hình hoạt động cho người dùng {self.env.user.login}: {str(e)}")
            # Return empty recordset instead of None to prevent crashes
            return self.browse()
    
    @api.constrains('api_key')
    def _check_api_key(self):
        """Validate API key format"""
        for record in self:
            if record.api_key:
                api_key = record.api_key.strip()
                if api_key and not api_key.startswith('sk-'):
                    raise ValidationError("Khóa API OpenAI phải bắt đầu bằng 'sk-'. Lấy khóa của bạn từ platform.openai.com")
    
    @api.constrains('temperature')
    def _check_temperature(self):
        """Validate temperature range"""
        for record in self:
            if record.temperature is not None and (record.temperature < 0.0 or record.temperature > 2.0):
                raise ValidationError("Nhiệt độ phải nằm trong khoảng từ 0.0 đến 2.0")
    
    @api.constrains('max_tokens')
    def _check_max_tokens(self):
        """Validate max tokens range"""
        for record in self:
            if record.max_tokens is not None and (record.max_tokens < 1 or record.max_tokens > 16384):
                raise ValidationError("Số token tối đa phải nằm trong khoảng từ 1 đến 16384 (GPT-4o-mini limit)")
    
    @api.constrains('top_p')
    def _check_top_p(self):
        """Validate top_p range"""
        for record in self:
            if record.top_p is not None and (record.top_p < 0.0 or record.top_p > 1.0):
                raise ValidationError("Top P phải nằm trong khoảng từ 0.0 đến 1.0")
    
    @api.constrains('frequency_penalty', 'presence_penalty')
    def _check_penalties(self):
        """Validate penalty ranges"""
        for record in self:
            if record.frequency_penalty is not None and (record.frequency_penalty < -2.0 or record.frequency_penalty > 2.0):
                raise ValidationError("Phạt tần suất phải nằm trong khoảng từ -2.0 đến 2.0")
            if record.presence_penalty is not None and (record.presence_penalty < -2.0 or record.presence_penalty > 2.0):
                raise ValidationError("Phạt hiện diện phải nằm trong khoảng từ -2.0 đến 2.0")
    
    

    @api.constrains('allow_delete')
    def _check_delete_permission(self):
        """Validate delete permission acknowledgment"""
        for record in self:
            if record.allow_delete:
                # Log the action for audit purposes
                _logger.warning(f"User {record.user_id.login} (ID: {record.user_id.id}) has enabled delete permission for config {record.id}")
                
                # Additional validation can be added here if needed
                # For example, check if user has admin rights, or require additional confirmation
                pass

    @api.constrains('token_limit_threshold')
    def _check_token_limit_threshold(self):
        """Validate token limit threshold range"""
        for record in self:
            if record.token_limit_threshold is not None and (record.token_limit_threshold < 1000 or record.token_limit_threshold > 200000):
                raise ValidationError("Token limit threshold phải nằm trong khoảng từ 1,000 đến 200,000")

    @api.constrains('token_safety_percentage')
    def _check_token_safety_percentage(self):
        """Validate token safety percentage range"""
        for record in self:
            if record.token_safety_percentage is not None and (record.token_safety_percentage < 50.0 or record.token_safety_percentage > 99.0):
                raise ValidationError("Token safety percentage phải nằm trong khoảng từ 50% đến 99%")

    

    def _get_config_data(self):
        """Get configuration data as dict for pending requests"""
        return {
            # Basic AI settings
            'api_key': self.api_key,
            'model_type': self.model_type,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
            'top_p': self.top_p,
            'frequency_penalty': self.frequency_penalty,
            'presence_penalty': self.presence_penalty,
            'system_prompt': self.system_prompt,
    
            # Legacy settings
            'rate_limit_per_hour': self.rate_limit_per_hour,
            'allow_delete': self.allow_delete,
            
            # AI Enhancement settings - Odoo 18 compatible
            'enable_context_memory': self.enable_context_memory,
            'context_window_size': self.context_window_size,
            'enable_user_profiling': self.enable_user_profiling,
            'response_optimization': self.response_optimization,
            
            # Advanced AI features
            'enable_streaming': self.enable_streaming,
            'enable_function_calling': self.enable_function_calling,
            'enable_web_search_enhanced': self.enable_web_search_enhanced,
            'enable_file_analysis': self.enable_file_analysis,
            
            # Error handling
            'fallback_response': self.fallback_response,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
            
            # Performance
            'cache_responses': self.cache_responses,
            'cache_duration': self.cache_duration,
            'response_timeout': self.response_timeout,
            
            # Odoo 18 integration
            'odoo_integration_level': self.odoo_integration_level,
            'enable_odoo_data_access': self.enable_odoo_data_access,
            'enable_odoo_actions': self.enable_odoo_actions,
        }

# =============================================================
#             GENERIC ORM FUNCTION HANDLERS
# ============================================================= 