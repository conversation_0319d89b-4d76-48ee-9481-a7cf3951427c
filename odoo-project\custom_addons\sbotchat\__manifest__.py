# -*- coding: utf-8 -*-
{
    'name': 'SBot Chat - Chatbot AI GPT-4 Mini',
    'version': '********.4',
    'category': 'Công cụ',
    'summary': 'Chatbot AI được hỗ trợ bởi OpenAI GPT-4 Mini API với lịch sử cuộc trò chuyện',
    'description': """
SBot Chat - Chatbot AI GPT-4 Mini
==================================

Một module chatbot AI toàn diện được hỗ trợ bởi OpenAI GPT-4 Mini API với các tính năng:
* Hỗ trợ mô hình gpt-4o-mini với hiệu suất cao và chi phí thấp
* Cài đặt API có thể tùy chỉnh
* Lịch sử cuộc trò chuyện với thanh bên
* Truy cập nút nổi toàn cục
* Giao diện chat thời gian thực

**HR Assistant Tích hợp** 🤖 (<PERSON>ê<PERSON> cầu cài đặt các module HR):
* Xử lý yêu cầu HR bằng ngôn ngữ tự nhiên (Tiếng Việt/English) khi các module HR được cài đặt
* Hỗ trợ quản lý nhân viên, chấm công, nghỉ phép, lương bổng, tuyển dụng, kỹ năng, dự án, bảo hiểm (nếu có module HR tương ứng)
* Báo cáo analytics và dashboard thống kê (yêu cầu module HR và analytic)
* Smart intent recognition và auto parameter extraction

Tương thích với Odoo 18.0

**🔄 Advanced AI Integration** (Version 2.0):
* Automatic agent reasoning với AI framework
* Enhanced memory management với conversation persistence
* Intelligent tool-calling automation 
* Improved error handling và reliability
* Pure AI Agent architecture

**🌐 Intelligent Web Browsing** (Version 3.0):
* AI-powered web content analysis với OpenAI GPT-4 Mini
* Deep web research với multi-source synthesis
* Intelligent content extraction và summarization
* Real-time web information với expert analysis
* Advanced token management cho large content
    """,
    'author': 'Công ty của bạn',
    'website': 'https://www.congtyban.com',
    'depends': [
        'base', 
        'web', 
        'mail',
        # Non-HR Dependencies
        'project',
        'calendar',
        'fleet',
        'analytic',
    ],
    'data': [
        'security/ir.model.access.csv',
        'security/sbotchat_security.xml',
        'views/sbotchat_views.xml',

        'views/vector_store_views.xml',
        'views/sbotchat_menus.xml',
        'data/sbotchat_cron.xml',
        'data/cron_jobs.xml',
        'data/enhanced_cron_jobs.xml',
    ],
    'external_dependencies': {
        'python': [
            'redis',  # 🚀 Phase 1: Redis for token optimization cache
            'openai',
            'requests',
            'numpy',
            'tiktoken',
            'beautifulsoup4',
            'markdown',
        ],
    },
    'assets': {
        'web.assets_backend': [
            'sbotchat/static/src/xml/sbotchat_templates.xml',
            'sbotchat/static/src/css/sbotchat.css',
            'sbotchat/static/src/css/ai_response_formatter.css',
            'sbotchat/static/src/js/ai_response_formatter.js',
            'sbotchat/static/src/js/sbotchat_widget.js',
            'sbotchat/static/src/js/sbotchat_floating.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': True,
    'license': 'LGPL-3',
}