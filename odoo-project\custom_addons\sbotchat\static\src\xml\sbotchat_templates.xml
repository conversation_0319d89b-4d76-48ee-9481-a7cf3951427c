<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <!-- Main Chat Interface Template - Premium Design 2025 -->
    <t t-name="sbotchat.ChatInterface">
        <div class="sbotchat-container">
            <!-- Sidebar with Conversations -->
            <div class="sbotchat-sidebar">
                <!-- Header with New Chat Button -->
                <div class="sbotchat-header">
                    <div class="header-title-section">
                        <h3>SBot Chat</h3>
                        <div class="status-badge-container">
                            <!-- Current: Online Status -->
                            <div class="status-indicator-modern">
                                <div class="status-dot-ring">
                                    <div class="status-dot-inner"></div>
                                </div>
                                <span class="status-text-modern">Trực tuyến</span>
                            </div>
                            
                            <!-- Other Status Examples (uncomment to use):
                            
                            Offline Status:
                            <div class="status-indicator-modern status-offline">
                                <div class="status-dot-ring">
                                    <div class="status-dot-inner"></div>
                                </div>
                                <span class="status-text-modern"><PERSON><PERSON><PERSON><PERSON> tuyến</span>
                            </div>
                            
                            Busy Status:
                            <div class="status-indicator-modern status-busy">
                                <div class="status-dot-ring">
                                    <div class="status-dot-inner"></div>
                                </div>
                                <span class="status-text-modern">Bận</span>
                            </div>
                            
                            Away Status:
                            <div class="status-indicator-modern status-away">
                                <div class="status-dot-ring">
                                    <div class="status-dot-inner"></div>
                                </div>
                                <span class="status-text-modern">Vắng mặt</span>
                            </div>
                            
                            -->
                        </div>
                    </div>
                    <button id="new-chat-btn" 
                            class="btn-sbotchat" 
                            t-att-disabled="state.isCreatingConversation">
                        <t t-if="state.isCreatingConversation">
                            <div class="loading-spinner" style="width: 16px; height: 16px; margin-right: 8px;"></div>
                            Đang tạo...
                        </t>
                        <t t-else="">
                            <i class="fa fa-plus" style="margin-right: 8px;"></i>
                            Cuộc trò chuyện mới
                        </t>
                    </button>
                </div>
                
                <!-- Conversations List -->
                <div class="sbotchat-conversations">
                    <t t-if="state.conversations.length === 0">
                        <div class="empty-state" style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                            <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">💬</div>
                            <p style="margin: 0; font-weight: 500;">Chưa có cuộc trò chuyện nào</p>
                            <p style="margin: 0.5rem 0 0 0; font-size: 0.875rem; opacity: 0.8;">Bắt đầu chat mới để bắt đầu</p>
                        </div>
                    </t>
                    <t t-else="">
                        <t t-foreach="state.conversations" t-as="conversation" t-key="conversation.id">
                            <div class="conversation-item" 
                                 t-att-class="conversation.id === state.currentConversationId ? 'active' : ''"
                                 t-on-click="() => this.selectConversation(conversation.id)">
                                <div class="conversation-content">
                                    <div class="conversation-title">
                                        <i class="fa fa-comment-o" style="margin-right: 8px; opacity: 0.7;"></i>
                                        <span t-att-data-conversation-id="conversation.id" class="conversation-title-text">
                                            <t t-esc="conversation.title"/>
                                        </span>
                                    </div>
                                    <div class="conversation-meta">
                                        <i class="fa fa-clock-o" style="margin-right: 4px;"></i>
                                        <t t-esc="conversation.last_message_date"/>
                                        <span style="margin-left: 8px;">
                                            <i class="fa fa-comment" style="margin-right: 4px;"></i>
                                            <t t-esc="conversation.message_count"/> tin nhắn
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- 3-dot menu -->
                                <div class="conversation-menu" t-on-click.stop="">
                                    <button class="conversation-menu-btn" 
                                            t-att-data-conversation-id="conversation.id"
                                            t-on-click.stop="(ev) => this.toggleConversationMenu(ev, conversation.id)">
                                        <i class="fa fa-ellipsis-v"></i>
                                    </button>
                                    <div class="conversation-menu-dropdown" t-att-id="'menu-' + conversation.id">
                                        <button class="conversation-menu-item"
                                                t-on-click.stop="(ev) => this.renameConversation(ev, conversation.id)">
                                            <i class="fa fa-edit"></i>
                                            Đổi tên
                                        </button>
                                        <button class="conversation-menu-item"
                                                t-on-click.stop="(ev) => this.duplicateConversation(ev, conversation.id)">
                                            <i class="fa fa-copy"></i>
                                            Sao chép
                                        </button>
                                        <button class="conversation-menu-item danger"
                                                t-on-click.stop="(ev) => this.deleteConversation(ev, conversation.id)">
                                            <i class="fa fa-trash"></i>
                                            Xóa
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </t>
                </div>
            </div>
            
            <!-- Main Chat Area -->
            <div class="sbotchat-main">
                <!-- Chat Header with Settings -->
                <div class="sbotchat-chat-header">
                    <h3>
                        <t t-if="state.currentConversation">
                            <i class="fa fa-comments" style="margin-right: 12px;"></i>
                            <t t-esc="state.currentConversation.title"/>
                        </t>
                        <t t-else="">
                            <i class="fa fa-robot" style="margin-right: 12px;"></i>
                            Trợ lý AI OpenAI GPT-4 Mini
                        </t>
                        <!-- HR Status Indicator -->
                        <div t-if="false" class="hr-status-indicator" style="margin-left: 10px; font-size: 0.8rem;">
                            <span class="hr-status-badge" style="background: rgba(16, 185, 129, 0.2); color: #10b981; padding: 2px 8px; border-radius: 12px; font-weight: 500;">
                                <i class="fa fa-users" style="margin-right: 4px;"></i>
                                HR Assistant Active
                            </span>
                        </div>
                    </h3>
                    
                    <div class="header-actions">
                        <!-- 🚀 PHASE 1: Redis Status Indicator with LED Effect -->
                        <div class="redis-status-indicator" 
                             t-att-class="`redis-status-indicator redis-${state.redisStatus}`"
                             t-att-title="`Redis: ${state.redisStatus.charAt(0).toUpperCase() + state.redisStatus.slice(1)}`"
                             style="margin-right: 8px; cursor: pointer;"
                             t-on-click="toggleCacheStats">
                        </div>
                        
                        <!-- HR Quick Actions -->
                        <div t-if="false" class="hr-quick-actions" style="display: flex; gap: 8px; margin-right: 10px;">
                            <button class="hr-quick-btn" title="Danh sách nhân viên" 
                                    style="background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.3); color: #3b82f6; padding: 6px 10px; border-radius: 6px; font-size: 0.8rem;"
                                    t-on-click="() => this.sendSuggestion('Hiển thị danh sách nhân viên')">
                                <i class="fa fa-users"></i>
                            </button>
                            <button class="hr-quick-btn" title="Chấm công hôm nay"
                                    style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); color: #10b981; padding: 6px 10px; border-radius: 6px; font-size: 0.8rem;"
                                    t-on-click="() => this.sendSuggestion('Báo cáo chấm công hôm nay')">
                                <i class="fa fa-clock-o"></i>
                            </button>
                            <button class="hr-quick-btn" title="Nghỉ phép chờ duyệt"
                                    style="background: rgba(245, 158, 11, 0.1); border: 1px solid rgba(245, 158, 11, 0.3); color: #f59e0b; padding: 6px 10px; border-radius: 6px; font-size: 0.8rem;"
                                    t-on-click="() => this.sendSuggestion('Danh sách nghỉ phép chờ duyệt')">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                        
                        <!-- HR Help Button -->
                        <button t-if="false" class="hr-help-btn" title="HR Assistant Help" 
                                style="background: rgba(147, 51, 234, 0.1); border: 1px solid rgba(147, 51, 234, 0.3); color: #9333ea; padding: 8px 12px; border-radius: 6px; margin-right: 8px;"
                                t-on-click="() => this.sendSuggestion('Hướng dẫn sử dụng HR Assistant')">
                            <i class="fa fa-question-circle"></i>
                        </button>
                        <button id="settings-btn" t-on-click="openSettings">
                            <i class="fa fa-cog"></i>
                        </button>
                        <!-- Close Button (X) to return to Odoo - Moved to RIGHT -->
                        <button id="close-chat-btn" t-on-click="closeChat" class="sbotchat-close-btn">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 🚀 PHASE 1: Cache Stats (Hidden by default, shown in tooltip) -->
                <!-- Cache statistics are now shown in tooltip when clicking the Redis status indicator -->

                <!-- HR Feedback Container -->
                <div t-if="false" class="sbotchat-hr-feedback" style="margin-bottom: 1rem;">
                    <!-- HR action feedback sẽ được hiển thị dynamic ở đây -->
                </div>
                
                <!-- Messages Area -->
                <div class="sbotchat-messages" id="messages-container">
                    <t t-if="state.messages.length === 0">
                        <div class="welcome-message" style="text-align: center; padding: 3rem 2rem; color: var(--text-secondary);">
                            <div style="font-size: 4rem; margin-bottom: 1.5rem;">🤖</div>
                            <h3 style="margin: 0 0 1rem 0; font-weight: 700; color: var(--text-primary);">
                                Chào mừng đến với SBot Chat!
                            </h3>
                            <p style="margin: 0 0 1rem 0; font-size: 1.1rem; opacity: 0.8;">
                                Tôi là trợ lý AI được hỗ trợ bởi OpenAI GPT-4 Mini. Hôm nay tôi có thể giúp gì cho bạn?
                            </p>
                            <div t-if="false" style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-top: 2rem;">
                                <div class="suggestion-chip" style="background: rgba(99, 102, 241, 0.1); border: 1px solid rgba(99, 102, 241, 0.3); padding: 0.75rem 1rem; border-radius: 1rem; cursor: pointer; transition: all 0.2s;" t-on-click="() => this.sendSuggestion('Hiển thị danh sách nhân viên')">
                                    <i class="fa fa-users" style="margin-right: 8px;"></i>
                                    Danh sách nhân viên
                                </div>
                                <div class="suggestion-chip" style="background: rgba(99, 102, 241, 0.1); border: 1px solid rgba(99, 102, 241, 0.3); padding: 0.75rem 1rem; border-radius: 1rem; cursor: pointer; transition: all 0.2s;" t-on-click="() => this.sendSuggestion('Tạo đơn nghỉ phép mới')">
                                    <i class="fa fa-calendar" style="margin-right: 8px;"></i>
                                    Tạo đơn nghỉ phép
                                </div>
                                <div class="suggestion-chip" style="background: rgba(99, 102, 241, 0.1); border: 1px solid rgba(99, 102, 241, 0.3); padding: 0.75rem 1rem; border-radius: 1rem; cursor: pointer; transition: all 0.2s;" t-on-click="() => this.sendSuggestion('Báo cáo chấm công tháng này')">
                                    <i class="fa fa-clock-o" style="margin-right: 8px;"></i>
                                    Báo cáo chấm công
                                </div>
                                <div class="suggestion-chip" style="background: rgba(99, 102, 241, 0.1); border: 1px solid rgba(99, 102, 241, 0.3); padding: 0.75rem 1rem; border-radius: 1rem; cursor: pointer; transition: all 0.2s;" t-on-click="() => this.sendSuggestion('Tính lương cho nhân viên')">
                                    <i class="fa fa-money" style="margin-right: 8px;"></i>
                                    Tính lương
                                </div>
                            </div>
                            
                            <!-- HR Suggestions Section -->
                            <div t-if="false" class="hr-suggestions-section" style="margin-top: 2rem;">
                                <div class="hr-suggestions-title" style="text-align: center; font-weight: 600; color: var(--text-primary); margin-bottom: 1rem;">
                                    <i class="fa fa-users" style="margin-right: 8px; color: var(--primary-color);"></i>
                                    HR Assistant Commands
                                </div>
                                <div class="sbotchat-suggestions" style="display: flex; gap: 0.75rem; justify-content: center; flex-wrap: wrap;">
                                    <!-- Employee Management -->
                                    <div class="hr-suggestion-chip" style="background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.3); color: #3b82f6; padding: 0.5rem 0.75rem; border-radius: 0.75rem; cursor: pointer; transition: all 0.2s; font-size: 0.8rem;" t-on-click="() => this.sendSuggestion('Tạo nhân viên mới')">
                                        <i class="fa fa-user-plus" style="margin-right: 6px;"></i>
                                        Tạo nhân viên
                                    </div>
                                    <div class="hr-suggestion-chip" style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); color: #10b981; padding: 0.5rem 0.75rem; border-radius: 0.75rem; cursor: pointer; transition: all 0.2s; font-size: 0.8rem;" t-on-click="() => this.sendSuggestion('Check-in nhân viên')">
                                        <i class="fa fa-sign-in" style="margin-right: 6px;"></i>
                                        Check-in
                                    </div>
                                    <div class="hr-suggestion-chip" style="background: rgba(245, 158, 11, 0.1); border: 1px solid rgba(245, 158, 11, 0.3); color: #f59e0b; padding: 0.5rem 0.75rem; border-radius: 0.75rem; cursor: pointer; transition: all 0.2s; font-size: 0.8rem;" t-on-click="() => this.sendSuggestion('Tạo đơn nghỉ phép')">
                                        <i class="fa fa-calendar-plus-o" style="margin-right: 6px;"></i>
                                        Đơn nghỉ phép
                                    </div>
                                    <div class="hr-suggestion-chip" style="background: rgba(147, 51, 234, 0.1); border: 1px solid rgba(147, 51, 234, 0.3); color: #9333ea; padding: 0.5rem 0.75rem; border-radius: 0.75rem; cursor: pointer; transition: all 0.2s; font-size: 0.8rem;" t-on-click="() => this.sendSuggestion('Dashboard HR stats')">
                                        <i class="fa fa-dashboard" style="margin-right: 6px;"></i>
                                        Dashboard
                                    </div>
                                    <div class="hr-suggestion-chip" style="background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); color: #ef4444; padding: 0.5rem 0.75rem; border-radius: 0.75rem; cursor: pointer; transition: all 0.2s; font-size: 0.8rem;" t-on-click="() => this.sendSuggestion('Quản lý BHXH BHYT')">
                                        <i class="fa fa-shield" style="margin-right: 6px;"></i>
                                        Bảo hiểm
                                    </div>
                                    <!-- Additional HR commands -->
                                    <div class="hr-suggestion-chip" style="background: rgba(34, 197, 94, 0.1); border: 1px solid rgba(34, 197, 94, 0.3); color: #22c55e; padding: 0.5rem 0.75rem; border-radius: 0.75rem; cursor: pointer; transition: all 0.2s; font-size: 0.8rem;" t-on-click="() => this.sendSuggestion('Tìm kiếm nhân viên')">
                                        <i class="fa fa-search" style="margin-right: 6px;"></i>
                                        Tìm kiếm
                                    </div>
                                    <div class="hr-suggestion-chip" style="background: rgba(168, 85, 247, 0.1); border: 1px solid rgba(168, 85, 247, 0.3); color: #a855f7; padding: 0.5rem 0.75rem; border-radius: 0.75rem; cursor: pointer; transition: all 0.2s; font-size: 0.8rem;" t-on-click="() => this.sendSuggestion('Báo cáo phân tích HR')">
                                        <i class="fa fa-line-chart" style="margin-right: 6px;"></i>
                                        Báo cáo
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                    <t t-else="">
                        <t t-foreach="state.messages" t-as="message" t-key="message.id">
                            <div t-att-class="'message ' + (message.role === 'user' ? 'user-message' : 'assistant-message') + (message.hr_action ? ' hr-action' : '') + (message.isError ? ' error' : '')">
                                <div class="message-content">
                                    <t t-if="message.role === 'user'">
                                        <i class="fa fa-user" style="margin-right: 8px; opacity: 0.8;"></i>
                                        <!-- User messages stay as plain text -->
                                        <span t-esc="message.content"/>
                                    </t>
                                    <t t-else="">
                                        <i t-att-class="'fa ' + (message.hr_action ? 'fa-users' : 'fa-robot')" style="margin-right: 8px; opacity: 0.8;"></i>
                                        <!-- AI Assistant messages use formatter -->
                                        <div class="ai-formatted-content" t-att-data-content="message.content" t-att-data-hr-action="message.hr_action" t-att-data-api-called="message.api_called" t-att-data-intent="message.intent"></div>
                                    </t>
                                    
                                    <!-- HR Action Info (deprecated - now handled by formatter) -->
                                    <t t-if="message.hr_action and message.api_called and !message.content.includes('🤖')">
                                        <div class="hr-action-info-legacy" style="margin-top: 8px; padding: 6px 10px; background: rgba(16, 185, 129, 0.1); border-radius: 4px; font-size: 0.8rem; color: var(--success);">
                                            <strong>🤖 API Called:</strong> <t t-esc="message.api_called"/>
                                            <br/><strong>Intent:</strong> <t t-esc="message.intent"/>
                                        </div>
                                    </t>
                                    
                                    <!-- Thinking Content for Assistant Messages -->
                                    <t t-if="message.role === 'assistant' and message.thinking">
                                        <div class="thinking-content">
                                            <details>
                                                <summary>
                                                    <i class="fa fa-brain" style="margin-right: 8px;"></i>
                                                    Xem quá trình suy luận của AI
                                                </summary>
                                                <div class="thinking-text">
                                                    <t t-esc="message.thinking"/>
                                                </div>
                                            </details>
                                        </div>
                                    </t>
                                </div>
                                <div class="message-meta">
                                    <t t-if="message.role === 'user'">
                                        <i class="fa fa-check" style="margin-right: 4px; color: var(--success);"></i>
                                        Bạn • <t t-esc="message.timestamp"/>
                                    </t>
                                    <t t-else="">
                                        <i t-att-class="'fa ' + (message.hr_action ? 'fa-users' : 'fa-robot')" style="margin-right: 4px; color: var(--primary-color);"></i>
                                        <t t-esc="message.hr_action ? 'HR Agent' : 'SBot'"/> • <t t-esc="message.timestamp"/>
                                        <t t-if="message.response_time">
                                            • <t t-esc="message.response_time"/>ms
                                        </t>
                                        <t t-if="message.tokens_used">
                                            • <t t-esc="message.tokens_used"/> tokens
                                        </t>
                                    </t>
                                </div>
                            </div>
                        </t>
                    </t>
                    
                    <!-- Typing Indicator -->
                    <div t-if="state.isTyping" class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
                
                <!-- Input Area -->
                <div class="sbotchat-input">
                    <div class="input-group">
                        <!-- Web Search Toggle Button -->
                        <button id="web-search-toggle" 
                                t-att-class="'web-search-toggle ' + (state.webSearchEnabled ? 'enabled' : 'disabled')"
                                t-on-click="toggleWebSearch"
                                title="Bật/Tắt tìm kiếm web">
                            <i class="fa fa-globe"></i>
                            <span class="web-search-text">
                                <t t-if="state.webSearchEnabled">WEB</t>
                                <t t-else="">WEB</t>
                            </span>
                        </button>
                        
                        <input type="text" 
                               id="message-input" 
                               t-att-placeholder="state.webSearchEnabled ? 'Nhập câu hỏi để tìm kiếm web và chat... (Enter để gửi)' : 'Nhập tin nhắn của bạn tại đây... (Nhấn Enter để gửi)'"
                               t-on-keydown="handleKeyDown"
                               autocomplete="off"
                               spellcheck="true"/>
                        <button id="send-btn" t-on-click="sendMessage" t-att-disabled="state.isLoading">
                            <t t-if="state.isLoading">
                                <div class="loading-spinner"></div>
                            </t>
                            <t t-else="">
                                <i class="fa fa-paper-plane"></i>
                            </t>
                        </button>
                    </div>
                    
                    <!-- 🚀 PHASE 1: Cache Status Bar - REMOVED to avoid UI clutter -->
                    <!-- Cache status is now shown in tooltip when clicking the Redis indicator -->
                    
                    <!-- Quick Actions -->
                    <div style="display: flex; gap: 0.5rem; margin-top: 1rem; flex-wrap: wrap;">
                        <!-- General Quick Actions -->
                        <button class="quick-action-btn" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); padding: 0.5rem 1rem; border-radius: 1rem; color: var(--text-secondary); cursor: pointer; transition: all 0.2s; font-size: 0.875rem;" t-on-click="() => this.insertText('Giải thích điều này: ')">
                            <i class="fa fa-question-circle" style="margin-right: 6px;"></i>
                            Giải thích
                        </button>
                        <button class="quick-action-btn" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); padding: 0.5rem 1rem; border-radius: 1rem; color: var(--text-secondary); cursor: pointer; transition: all 0.2s; font-size: 0.875rem;" t-on-click="() => this.insertText('Tóm tắt: ')">
                            <i class="fa fa-compress" style="margin-right: 6px;"></i>
                            Tóm tắt
                        </button>
                        <button class="quick-action-btn" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); padding: 0.5rem 1rem; border-radius: 1rem; color: var(--text-secondary); cursor: pointer; transition: all 0.2s; font-size: 0.875rem;" t-on-click="() => this.insertText('Viết code cho: ')">
                            <i class="fa fa-code" style="margin-right: 6px;"></i>
                            Lập trình
                        </button>
                        
                        <!-- HR Quick Actions -->
                        <button t-if="false" class="quick-action-btn hr-quick-action" style="background: rgba(59, 130, 246, 0.15); border: 1px solid rgba(59, 130, 246, 0.3); padding: 0.5rem 1rem; border-radius: 1rem; color: #3b82f6; cursor: pointer; transition: all 0.2s; font-size: 0.875rem;" t-on-click="() => this.insertText('Hiển thị danh sách nhân viên ')">
                            <i class="fa fa-users" style="margin-right: 6px;"></i>
                            Nhân viên
                        </button>
                        <button t-if="false" class="quick-action-btn hr-quick-action" style="background: rgba(16, 185, 129, 0.15); border: 1px solid rgba(16, 185, 129, 0.3); padding: 0.5rem 1rem; border-radius: 1rem; color: #10b981; cursor: pointer; transition: all 0.2s; font-size: 0.875rem;" t-on-click="() => this.insertText('Báo cáo chấm công ')">
                            <i class="fa fa-clock-o" style="margin-right: 6px;"></i>
                            Chấm công
                        </button>
                        <button t-if="false" class="quick-action-btn hr-quick-action" style="background: rgba(245, 158, 11, 0.15); border: 1px solid rgba(245, 158, 11, 0.3); padding: 0.5rem 1rem; border-radius: 1rem; color: #f59e0b; cursor: pointer; transition: all 0.2s; font-size: 0.875rem;" t-on-click="() => this.insertText('Tạo đơn nghỉ phép ')">
                            <i class="fa fa-calendar" style="margin-right: 6px;"></i>
                            Nghỉ phép
                        </button>
                        
                        <!-- Clear Chat Action -->
                        <button class="quick-action-btn" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); padding: 0.5rem 1rem; border-radius: 1rem; color: var(--text-secondary); cursor: pointer; transition: all 0.2s; font-size: 0.875rem;" t-on-click="clearMessages">
                            <i class="fa fa-trash" style="margin-right: 6px;"></i>
                            Xóa chat
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Right Panel - Dynamic Form Creator (500px Fixed) -->
            <!-- ĐÃ XOÁ TOÀN BỘ DYNAMIC FORM CREATOR -->
        </div>
        
        <!-- Settings Modal -->
        <div t-if="state.showSettings" class="sbotchat-modal" t-on-click="closeSettingsOnBackdrop">
            <div class="sbotchat-modal-content" t-on-click.stop="">
                <div class="sbotchat-modal-header">
                    <h4>
                        <i class="fa fa-cog" style="margin-right: 12px;"></i>
                        Cài đặt &amp; Cấu hình
                    </h4>
                    <button class="sbotchat-modal-close" t-on-click="closeSettings">
                        ×
                    </button>
                </div>
                <div class="sbotchat-modal-body">
                    <!-- Settings Form -->
                    <div style="padding: 2rem;">
                        <!-- API Configuration Section -->
                        <div class="settings-section" style="margin-bottom: 2rem;">
                            <h5 style="margin: 0 0 1rem 0; font-weight: 600; color: var(--text-primary); display: flex; align-items: center;">
                                <i class="fa fa-key" style="margin-right: 8px; color: var(--primary-color);"></i>
                                Cấu hình API
                            </h5>
                            
                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <label for="api-key" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary);">
                                    <i class="fa fa-lock" style="margin-right: 6px;"></i>
                                    Khóa API OpenAI
                                </label>
                                <input type="password" 
                                       id="api-key" 
                                       placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                       style="width: 100%; padding: 0.75rem 1rem; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.5rem; background: rgba(255, 255, 255, 0.1); color: var(--text-primary); backdrop-filter: blur(10px); transition: all 0.2s;"
                                       t-att-value="state.config.api_key || ''"/>
                                <small style="color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.25rem; display: block;">
                                    <i class="fa fa-info-circle" style="margin-right: 4px;"></i>
                                    Lấy khóa API của bạn từ <a href="https://platform.openai.com" target="_blank" style="color: var(--primary-color);">OpenAI Platform</a>
                                </small>
                            </div>
                            
                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <label for="brave-search-api-key" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary);">
                                    <i class="fa fa-globe" style="margin-right: 6px;"></i>
                                    Khóa API Brave Search
                                </label>
                                <input type="password" 
                                       id="brave-search-api-key" 
                                       placeholder="BSAPixxxxxxxxxxxxxxxxxxxxxxxx"
                                       style="width: 100%; padding: 0.75rem 1rem; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.5rem; background: rgba(255, 255, 255, 0.1); color: var(--text-primary); backdrop-filter: blur(10px); transition: all 0.2s;"
                                       t-att-value="state.config.brave_search_api_key || ''"/>
                                <small style="color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.25rem; display: block;">
                                    <i class="fa fa-info-circle" style="margin-right: 4px;"></i>
                                    Lấy khóa API của bạn từ <a href="https://search.brave.com/search-api" target="_blank" style="color: var(--primary-color);">Brave Search API</a>
                                </small>
                            </div>
                            
                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <label for="default-vector-store" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary);">
                                    <i class="fa fa-database" style="margin-right: 6px;"></i>
                                    Vector Store mặc định
                                </label>
                                <input type="text"
                                       id="default-vector-store"
                                       placeholder="vs_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                       style="width: 100%; padding: 0.75rem 1rem; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.5rem; background: rgba(255, 255, 255, 0.1); color: var(--text-primary); backdrop-filter: blur(10px); transition: all 0.2s;"
                                       t-att-value="state.config.default_vector_store_id_name || ''"/>
                                <small style="color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.25rem; display: block;">
                                    <i class="fa fa-info-circle" style="margin-right: 4px;"></i>
                                    Chọn hoặc dán ID Vector Store OpenAI cần dùng mặc định
                                </small>
                            </div>
                        </div>
                        
                        <!-- Model Configuration Section -->
                        <div class="settings-section" style="margin-bottom: 2rem;">
                            <h5 style="margin: 0 0 1rem 0; font-weight: 600; color: var(--text-primary); display: flex; align-items: center;">
                                <i class="fa fa-brain" style="margin-right: 8px; color: var(--primary-color);"></i>
                                Cấu hình mô hình
                            </h5>
                            
                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <label for="model-type" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary);">
                                    <i class="fa fa-microchip" style="margin-right: 6px;"></i>
                                    Loại mô hình
                                </label>
                                <select id="model-type" 
                                        style="width: 100%; padding: 0.75rem 1rem; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.5rem; background: rgba(255, 255, 255, 0.1); color: var(--text-primary); backdrop-filter: blur(10px);">
                                    <option value="gpt-4o-mini">GPT-4o Mini (Khuyến nghị)</option>
                                    <option value="gpt-4o">GPT-4o (Cao cấp)</option>
                                    <option value="gpt-4-turbo">GPT-4 Turbo (Mạnh nhất)</option>
                                </select>
                            </div>
                            
                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <label for="temperature" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary);">
                                    <i class="fa fa-thermometer-half" style="margin-right: 6px;"></i>
                                    Nhiệt độ: <span id="temperature-value">1.0</span>
                                </label>
                                <input type="range" 
                                       id="temperature" 
                                       min="0" 
                                       max="2" 
                                       step="0.1" 
                                       value="1.0"
                                       style="width: 100%; margin-bottom: 0.5rem;"
                                       t-on-input="updateTemperatureDisplay"/>
                                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; color: var(--text-secondary);">
                                    <span>Tập trung hơn</span>
                                    <span>Sáng tạo hơn</span>
                                </div>
                            </div>
                            
                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <label for="max-tokens" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary);">
                                    <i class="fa fa-text-width" style="margin-right: 6px;"></i>
                                    Số token tối đa
                                </label>
                                <input type="number" 
                                       id="max-tokens" 
                                       min="100" 
                                       max="8000" 
                                       value="4000"
                                       style="width: 100%; padding: 0.75rem 1rem; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.5rem; background: rgba(255, 255, 255, 0.1); color: var(--text-primary); backdrop-filter: blur(10px);"/>
                                <small style="color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.25rem; display: block;">
                                    <i class="fa fa-info-circle" style="margin-right: 4px;"></i>
                                    Độ dài tối đa của phản hồi AI
                                </small>
                            </div>

                            <!-- Rate limit per hour -->
                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <label for="rate-limit" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary);">
                                    <i class="fa fa-tachometer" style="margin-right: 6px;"></i>
                                    Giới hạn request mỗi giờ
                                </label>
                                <input type="number"
                                       id="rate-limit"
                                       min="10"
                                       max="10000"
                                       value="100"
                                       style="width: 100%; padding: 0.75rem 1rem; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.5rem; background: rgba(255, 255, 255, 0.1); color: var(--text-primary); backdrop-filter: blur(10px);"/>
                                <small style="color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.25rem; display: block;">
                                    <i class="fa fa-info-circle" style="margin-right: 4px;"></i>
                                    Số tin nhắn tối đa gửi tới AI mỗi giờ của người dùng
                                </small>
                            </div>

                            <!-- Allow delete (toggle switch) -->
                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <div style="display:flex;justify-content:space-between;align-items:center;gap:1rem;">
                                    <label for="allow-delete" style="font-weight: 500; color: var(--text-primary); margin:0;display:flex;align-items:center;gap:6px;">
                                        <i class="fa fa-trash"></i>
                                        Cho phép xoá dữ liệu qua Chatbot
                                    </label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="allow-delete" />
                                        <label for="allow-delete"></label>
                                    </div>
                                </div>
                                <small style="display:block;margin-top:0.25rem;color:var(--text-secondary);font-size:0.75rem;">
                                    CẢNH BÁO: Khi bật, chatbot có thể xoá bản ghi dữ liệu vĩnh viễn. Bạn phải chịu toàn bộ trách nhiệm. Hãy cân nhắc kỹ trước khi kích hoạt.
                                </small>
                            </div>

                            <!-- 🚀 PHASE 1: Redis Cache Settings -->
                            <div class="form-group" style="margin-bottom: 2rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 8px; padding: 1rem; background: rgba(59, 130, 246, 0.05);">
                                <h4 style="margin: 0 0 1rem 0; color: #3b82f6; font-size: 1rem; font-weight: 600;">
                                    <i class="fa fa-rocket" style="margin-right: 6px;"></i>
                                    Phase 1: Token Optimization
                                </h4>
                                
                                <div style="display:flex;justify-content:space-between;align-items:center;gap:1rem; margin-bottom: 1rem;">
                                    <label for="cache-enabled" style="font-weight: 500; color: var(--text-primary); margin:0;display:flex;align-items:center;gap:6px;">
                                        <i class="fa fa-database"></i>
                                        Enable Redis Cache (85% Token Reduction)
                                    </label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="cache-enabled" t-att-checked="state.config.cache_responses"/>
                                        <label for="cache-enabled"></label>
                                    </div>
                                </div>
                                
                                <div t-if="state.config.cache_responses" style="margin-left: 20px; margin-bottom: 1rem;">
                                    <div class="form-group" style="margin-bottom: 1rem;">
                                        <label for="redis-host" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary); font-size: 0.9rem;">
                                            Redis Host
                                        </label>
                                        <input type="text" 
                                               id="redis-host" 
                                               t-att-value="state.config.redis_host"
                                               placeholder="localhost"
                                               style="width: 100%; padding: 0.5rem 0.75rem; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.5rem; background: rgba(255, 255, 255, 0.1); color: var(--text-primary); font-size: 0.9rem;"/>
                                    </div>
                                    
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                        <div class="form-group">
                                            <label for="redis-port" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary); font-size: 0.9rem;">
                                                Port
                                            </label>
                                            <input type="number" 
                                                   id="redis-port" 
                                                   t-att-value="state.config.redis_port"
                                                   placeholder="6379"
                                                   style="width: 100%; padding: 0.5rem 0.75rem; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.5rem; background: rgba(255, 255, 255, 0.1); color: var(--text-primary); font-size: 0.9rem;"/>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="cache-duration" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary); font-size: 0.9rem;">
                                                Cache Duration (hours)
                                            </label>
                                            <input type="number" 
                                                   id="cache-duration" 
                                                   t-att-value="state.config.cache_duration"
                                                   min="1"
                                                   max="168"
                                                   placeholder="6"
                                                   style="width: 100%; padding: 0.5rem 0.75rem; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.5rem; background: rgba(255, 255, 255, 0.1); color: var(--text-primary); font-size: 0.9rem;"/>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.2); border-radius: 6px; padding: 8px; margin-top: 8px;">
                                    <div style="font-size: 0.8rem; color: #10b981; font-weight: 500;">
                                        <i class="fa fa-check-circle" style="margin-right: 4px;"></i>
                                        Phase 1 Benefits: 85% token reduction, 2x speed, ROI 165%
                                    </div>
                                </div>
                            </div>

                            <!-- Web Search Settings -->
                            <div class="form-group" style="margin-bottom: 2rem;">
                                <div style="display:flex;justify-content:space-between;align-items:center;gap:1rem;">
                                    <label for="enable-web-search" style="font-weight: 500; color: var(--text-primary); margin:0;display:flex;align-items:center;gap:6px;">
                                        <i class="fa fa-globe"></i>
                                        Bật tìm kiếm web mặc định
                                    </label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="enable-web-search" />
                                        <label for="enable-web-search"></label>
                                    </div>
                                </div>
                                <small style="display:block;margin-top:0.25rem;color:var(--text-secondary);font-size:0.75rem;">
                                    💡 Khi bật, AI sẽ tự động tìm kiếm thông tin mới nhất trên web để trả lời câu hỏi. Bạn vẫn có thể bật/tắt bằng nút WEB bên dưới input.
                                </small>
                            </div>
                        </div>
                        
                        <!-- System Prompt Section -->
                        <div class="settings-section" style="margin-bottom: 2rem;">
                            <h5 style="margin: 0 0 1rem 0; font-weight: 600; color: var(--text-primary); display: flex; align-items: center;">
                                <i class="fa fa-edit" style="margin-right: 8px; color: var(--primary-color);"></i>
                                Lời nhắc hệ thống
                            </h5>
                            
                            <div class="form-group">
                                <label for="system-prompt" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--text-primary);">
                                    <i class="fa fa-file-text-o" style="margin-right: 6px;"></i>
                                    Hướng dẫn tùy chỉnh
                                </label>
                                <textarea id="system-prompt" 
                                          rows="4" 
                                          placeholder="Bạn là một trợ lý AI hữu ích. Hãy ngắn gọn và chính xác trong các phản hồi của bạn."
                                          style="width: 100%; padding: 0.75rem 1rem; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.5rem; background: rgba(255, 255, 255, 0.1); color: var(--text-primary); backdrop-filter: blur(10px); resize: vertical; min-height: 100px;"></textarea>
                                <small style="color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.25rem; display: block;">
                                    <i class="fa fa-lightbulb-o" style="margin-right: 4px;"></i>
                                    Định nghĩa cách AI nên hoạt động và phản hồi
                                </small>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div style="display: flex; gap: 1rem; justify-content: flex-end; padding-top: 1rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                            <button class="btn-secondary" 
                                    style="padding: 0.75rem 1.5rem; border: 1px solid rgba(255, 255, 255, 0.2); background: rgba(255, 255, 255, 0.1); color: var(--text-secondary); border-radius: 0.5rem; cursor: pointer; transition: all 0.2s;"
                                    t-on-click="resetSettings">
                                <i class="fa fa-undo" style="margin-right: 6px;"></i>
                                Đặt lại mặc định
                            </button>
                            <button class="btn-sbotchat" t-on-click="saveConfig">
                                <i class="fa fa-save" style="margin-right: 6px;"></i>
                                Lưu cấu hình
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
    
    <!-- Global Floating Button Template -->
    <t t-name="sbotchat.FloatingButton">
        <div class="sbotchat-global-floating-btn" 
             t-att-class="state.isBlinking ? 'blinking' : ''"
             t-on-click="openChat">
            <span class="sbot-text">SBOT</span>
            
            <!-- Pending Requests Indicator -->
            <div t-if="state.pendingCount > 0" 
                 class="pending-indicator"
                 t-esc="state.pendingCount"></div>
            
            <!-- Notification Indicator -->
            <div t-if="state.hasNotification" 
                 class="notification-indicator"></div>
            
            <!-- Dynamic Tooltip -->
            <div class="tooltip" 
                 t-att-class="state.hasNotification ? 'has-notification' : (state.pendingCount > 0 ? 'has-pending' : '')">
                <t t-if="state.hasNotification">
                    <t t-esc="state.notificationMessage"/>
                </t>
                <t t-elif="state.pendingCount > 0">
                    <t t-esc="state.pendingCount"/> tin nhắn đang xử lý
                </t>
                <t t-else="">
                    Mở SBot Chat
                </t>
            </div>
            
            <!-- Notification Text for Blinking State -->
            <div t-if="state.isBlinking" 
                 class="notification-text"
                 t-esc="state.notificationMessage"></div>
        </div>
    </t>
</templates> 