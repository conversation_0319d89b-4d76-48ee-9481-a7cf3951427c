id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_sbotchat_config_user,sbotchat.config.user,sbotchat.model_sbotchat_config,base.group_user,1,1,1,1
access_sbotchat_conversation_user,sbotchat.conversation.user,sbotchat.model_sbotchat_conversation,base.group_user,1,1,1,1
access_sbotchat_message_user,sbotchat.message.user,sbotchat.model_sbotchat_message,base.group_user,1,1,1,1
access_sbotchat_pending_request_user,sbotchat.pending.request.user,sbotchat.model_sbotchat_pending_request,base.group_user,1,1,1,1
access_sbotchat_notification_user,sbotchat.notification.user,sbotchat.model_sbotchat_notification,base.group_user,1,1,1,1
access_sbotchat_notification_manager_user,sbotchat.notification.manager.user,sbotchat.model_sbotchat_notification_manager,base.group_user,1,1,1,0

access_project_project_ai_user,project.project.ai.user,project.model_project_project,base.group_user,1,1,1,0
access_project_task_ai_user,project.task.ai.user,project.model_project_task,base.group_user,1,1,1,0
access_timesheet_ai_user,timesheet.ai.user,analytic.model_account_analytic_line,base.group_user,1,1,1,0
access_sbotchat_security_framework_user,sbotchat.security.framework.user,sbotchat.model_sbotchat_security_framework,base.group_user,1,0,0,0
access_sbotchat_security_framework_admin,sbotchat.security.framework.admin,sbotchat.model_sbotchat_security_framework,base.group_system,1,1,1,1
access_sbotchat_reasoning_user,sbotchat.reasoning.user,sbotchat.model_sbotchat_reasoning,base.group_user,1,1,1,0
access_sbotchat_reasoning_template_user,sbotchat.reasoning.template.user,sbotchat.model_sbotchat_reasoning_template,base.group_user,1,1,1,0
access_sbotchat_validation_user,sbotchat.validation.user,sbotchat.model_sbotchat_validation,base.group_user,1,1,1,0
access_sbotchat_validation_preset_user,sbotchat.validation.preset.user,sbotchat.model_sbotchat_validation_preset,base.group_user,1,1,1,0
access_sbotchat_enhanced_memory_user,sbotchat.enhanced.memory.user,sbotchat.model_sbotchat_enhanced_memory,base.group_user,1,1,1,0
access_sbotchat_multi_agent_system_user,sbotchat.multi.agent.system.user,sbotchat.model_sbotchat_multi_agent_system,base.group_user,1,1,1,0
access_sbotchat_specialized_agent_user,sbotchat.specialized.agent.user,sbotchat.model_sbotchat_specialized_agent,base.group_user,1,1,1,0
access_sbotchat_advanced_tool_user,sbotchat.advanced.tool.user,sbotchat.model_sbotchat_advanced_tool,base.group_user,1,1,1,0
access_sbotchat_performance_user,sbotchat.performance.user,sbotchat.model_sbotchat_performance,base.group_user,1,1,1,0
access_sbotchat_workflow_template_user,sbotchat.workflow.template.user,sbotchat.model_sbotchat_workflow_template,base.group_user,1,1,1,0
access_sbotchat_workflow_execution_user,sbotchat.workflow.execution.user,sbotchat.model_sbotchat_workflow_execution,base.group_user,1,1,0,0
access_sbotchat_vector_store_user,sbotchat.vector.store.user,sbotchat.model_sbotchat_vector_store,base.group_user,1,1,1,1
access_sbotchat_vector_store_file_user,sbotchat.vector.store.file.user,sbotchat.model_sbotchat_vector_store_file,base.group_user,1,1,1,1
access_sbotchat_file_upload_user,sbotchat.file.upload.user,sbotchat.model_sbotchat_file_upload,base.group_user,1,1,1,1
access_sbotchat_vector_store_tools_user,sbotchat.vector.store.tools.user,sbotchat.model_sbotchat_vector_store_tools,base.group_user,1,0,0,0
access_sbotchat_redis_manager_user,sbotchat.redis.manager.user,sbotchat.model_sbotchat_redis_manager,base.group_user,1,1,1,0
access_sbotchat_redis_manager_admin,sbotchat.redis.manager.admin,sbotchat.model_sbotchat_redis_manager,base.group_system,1,1,1,1
access_sbotchat_cache_service_user,sbotchat.cache.service.user,sbotchat.model_sbotchat_cache_service,base.group_user,1,1,1,0
access_sbotchat_cache_service_admin,sbotchat.cache.service.admin,sbotchat.model_sbotchat_cache_service,base.group_system,1,1,1,1
access_sbotchat_redis_service_user,sbotchat.redis.service.user,sbotchat.model_sbotchat_redis_service,base.group_user,1,1,1,0
access_sbotchat_redis_service_admin,sbotchat.redis.service.admin,sbotchat.model_sbotchat_redis_service,base.group_system,1,1,1,1