# =============================================
# SBotChat Module Dependencies
# Updated: 2025-06-30
# Compatible với Odoo 18.0 và Python 3.11+
# =============================================



# Modern memory management

# LLM Integration

openai>=1.90.0  # Latest cho DeepSeek API compatibility

# Modern Pydantic v2
pydantic>=2.11.7,<3.0
pydantic-core>=2.33.2,<3.0
pydantic-settings>=2.10.0,<3.0

# Core Async & HTTP Dependencies  
tiktoken>=0.9.0
python-dotenv>=1.0.1
aiohttp>=3.12.13
httpx>=0.28.1  # Modern async HTTP client
httpx-sse>=0.4.0  # Server-sent events support

# AI & ML Dependencies
sentence-transformers>=4.1.0  # Vector embeddings cho enhanced memory
transformers>=4.53.0  # Hugging Face transformers
numpy>=1.26.4  # Numerical computing

# Memory and persistence
redis>=6.2.0  # Redis shared memory system
sqlalchemy>=2.0.30  # Conversation persistence

# System monitoring
psutil>=5.9.4  # System performance monitoring

# Core web dependencies (already in Odoo but explicit versions)
requests>=2.31.0
psycopg2-binary>=2.9.9

# =============================================
# Optional Dependencies (Auto-fallback if missing)
# =============================================
# Nếu không có sentence-transformers: fallback to simple embeddings
# Nếu không có redis: fallback to local memory
# Nếu không có psutil: disable performance monitoring 