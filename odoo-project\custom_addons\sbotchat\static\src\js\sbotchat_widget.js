/** @odoo-module **/

import { Component, useState, onMounted, onWillUnmount } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * SBot Chat Widget - Premium Modern Design 2025
 * Advanced AI Chat Interface with DeepSeek Integration and HR Dashboard
 */
class SbotchatWidget extends Component {
  static template = "sbotchat.ChatInterface";

  setup() {
    // Initialize reactive state first
    this.state = useState({
      messages: [],
      conversations: [],
      currentConversationId: null,
      currentConversation: null,
      isLoading: false,
      isTyping: false,
      isCreatingConversation: false,
      showSettings: false,
      config: {
        api_key: "",
        model: "deepseek-chat",
        temperature: 1.0,
        max_tokens: 4000,
        rate_limit_per_hour: 100,
        system_prompt: "",
        allow_delete: false,
        // 🚀 PHASE 1: Redis Configuration
        redis_enabled: true,
        redis_host: "localhost",
        redis_port: 6379,
        cache_responses: true,
        cache_duration: 6,
      },
      lastMessageId: 0,
      connectionStatus: "online", // online, offline, thinking
      userTyping: false,
      hrSuggestions: [],
      webSearchEnabled: false, // Web search toggle state
      // 🚀 PHASE 1: Redis Cache State
      redisStatus: "checking", // checking, online, offline, error
      cacheStats: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        tokensSaved: 0,
      },
      showCacheStats: false,
      cacheEnabled: true,
    });

    // Auto-refresh intervals
    this.dashboardRefreshInterval = null;
    this.realtimeUpdateInterval = null;
    this.historyRefreshInterval = null;
    this.clockInterval = null;

    // Auto-scroll and typing detection
    this.autoScrollTimeout = null;
    this.typingTimeout = null;

    // Initialize component
    onMounted(async () => {
      try {
        await this.initializeWidget();
        // 🔗 Make widget available for Dynamic Form Generator
        window.sbotchatWidget = this;

        // Initialize Redis cache system with proper status checking
        await this.initializeRedisCache();

        console.log("✅ SBotChat Widget fully initialized");
      } catch (error) {
        console.error("❌ Widget initialization failed:", error);
        this.state.redisStatus = "error";
        this.render();
      }
    });

    // Cleanup on unmount
    onWillUnmount(() => {
      this.cleanup();
    });
  }

  /**
   * Safe service usage with fallback
   */
  useServiceSafe(serviceName) {
    try {
      return useService(serviceName);
    } catch (error) {
      console.warn(`Service ${serviceName} not available, using fallback`);
      return this.createFallbackService(serviceName);
    }
  }

  /**
   * Create fallback services for better compatibility
   */
  createFallbackService(serviceName) {
    const fallbacks = {
      rpc: async (route, params) => {
        try {
          // Get CSRF token from multiple sources
          let csrfToken = null;

          // Try meta tag first
          const metaToken = document
            .querySelector('meta[name="csrf-token"]')
            ?.getAttribute("content");
          if (metaToken) {
            csrfToken = metaToken;
          }

          // Try hidden input in forms
          if (!csrfToken) {
            const hiddenToken = document.querySelector(
              'input[name="csrf_token"]'
            )?.value;
            if (hiddenToken) {
              csrfToken = hiddenToken;
            }
          }

          // Try global variables
          if (!csrfToken) {
            csrfToken =
              window.odoo?.csrf_token ||
              window.csrf_token ||
              window.odoo?.__session_info__?.csrf_token;
          }

          // Prepare headers with proper authentication
          const headers = {
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest",
          };

          // Add CSRF token if available
          if (csrfToken) {
            headers["X-CSRFToken"] = csrfToken;
          }

          // Get session ID from cookies
          const sessionId = this.getSessionId();

          // Debug logging for authentication
          console.log("🔐 Authentication Debug:", {
            route: route,
            hasCSRF: !!csrfToken,
            hasSession: !!sessionId,
            csrfSource: csrfToken ? "found" : "missing",
          });

          const response = await fetch(route, {
            method: "POST",
            headers: headers,
            credentials: "include", // Include cookies for session
            body: JSON.stringify({
              jsonrpc: "2.0",
              method: "call",
              params: params,
              id: Date.now(),
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();

          // Check for Odoo-specific errors
          if (data.error) {
            console.error("Odoo RPC Error:", data.error);

            // Handle session expired
            if (data.error.data?.name === "odoo.http.SessionExpiredException") {
              this.handleSessionExpired();
              throw new Error("Session expired. Please refresh the page.");
            }

            throw new Error(data.error.message || "RPC Error");
          }

          return data.result;
        } catch (error) {
          console.error("RPC fallback error:", error);
          throw error;
        }
      },
      orm: {
        call: async (model, method, args, kwargs) => {
          console.log("ORM fallback call:", model, method, args, kwargs);
          return {};
        },
        search: async (model, domain, options) => {
          console.log("ORM fallback search:", model, domain, options);
          return [];
        },
        read: async (model, ids, fields) => {
          console.log("ORM fallback read:", model, ids, fields);
          return [];
        },
      },
      user: {
        userId: 1,
        isAdmin: false,
        name: "User",
        context: {},
      },
      notification: {
        add: (message, options = {}) => {
          this.showNotification(message, options.type || "info");
        },
      },
      action: {
        doAction: (action) => {
          console.log("Action fallback:", action);
        },
      },
    };
    return fallbacks[serviceName] || {};
  }

  /**
   * Get session ID from cookies
   */
  getSessionId() {
    const cookies = document.cookie.split(";");
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split("=");
      if (name === "session_id") {
        return value;
      }
    }
    return null;
  }

  /**
   * Handle session expired error
   */
  handleSessionExpired() {
    console.warn("🔑 Session expired, redirecting to login...");
    this.showNotification(
      "Phiên đăng nhập đã hết hạn. Đang chuyển hướng...",
      "warning"
    );

    // Redirect to login after a short delay
    setTimeout(() => {
      window.location.href = "/web/login";
    }, 2000);
  }

  /**
   * Enhanced RPC call with multiple fallback strategies and performance optimization
   */
  async rpcCall(route, params = {}) {
    const startTime = performance.now();

    // Performance optimization: Cache frequently used endpoints
    const cacheKey = `${route}_${JSON.stringify(params)}`;
    if (
      this.rpcCache &&
      this.rpcCache[cacheKey] &&
      Date.now() - this.rpcCache[cacheKey].timestamp < 30000
    ) {
      // 30s cache
      this.logPerformance("rpcCall", startTime, "cached");
      return this.rpcCache[cacheKey].data;
    }

    // Strategy 1: Try native Odoo RPC if available
    if (this.rpc && typeof this.rpc === "function") {
      try {
        console.log("🔄 Using native RPC for:", route);
        const result = await this.rpc(route, params);
        this.cacheRpcResult(cacheKey, result);
        this.logPerformance("rpcCall", startTime, "native");
        return result;
      } catch (error) {
        console.warn("⚠️ Native RPC failed, trying fallback:", error);
      }
    }

    // Strategy 2: Try enhanced fetch with proper authentication
    try {
      console.log("🔄 Using enhanced fetch for:", route);
      const result = await this.enhancedFetch(route, params);
      this.cacheRpcResult(cacheKey, result);
      this.logPerformance("rpcCall", startTime, "enhanced");
      return result;
    } catch (error) {
      console.error("❌ All RPC strategies failed:", error);
      throw error;
    }
  }

  /**
   * Cache RPC results for performance
   */
  cacheRpcResult(key, data) {
    if (!this.rpcCache) this.rpcCache = {};

    // Limit cache size to prevent memory leaks
    const cacheKeys = Object.keys(this.rpcCache);
    if (cacheKeys.length > 50) {
      // Remove oldest entries
      const oldestKey = cacheKeys.sort(
        (a, b) => this.rpcCache[a].timestamp - this.rpcCache[b].timestamp
      )[0];
      delete this.rpcCache[oldestKey];
    }

    this.rpcCache[key] = {
      data: data,
      timestamp: Date.now(),
    };
  }

  /**
   * Log performance metrics
   */
  logPerformance(operation, startTime, method) {
    const duration = performance.now() - startTime;

    if (!this.performanceMetrics) this.performanceMetrics = [];

    this.performanceMetrics.push({
      operation,
      duration,
      method,
      timestamp: Date.now(),
    });

    // Keep only last 100 metrics
    if (this.performanceMetrics.length > 100) {
      this.performanceMetrics = this.performanceMetrics.slice(-100);
    }

    // Log slow operations
    if (duration > 1000) {
      console.warn(
        `⚠️ Slow ${operation} (${method}): ${duration.toFixed(2)}ms`
      );
    } else if (duration < 100) {
      console.log(`⚡ Fast ${operation} (${method}): ${duration.toFixed(2)}ms`);
    }

    // Update performance indicator
    this.updatePerformanceIndicator(duration);
  }

  /**
   * Update performance indicator
   */
  updatePerformanceIndicator(duration) {
    let indicator = document.querySelector(".performance-indicator");
    if (!indicator) {
      indicator = document.createElement("div");
      indicator.className = "performance-indicator";
      document.body.appendChild(indicator);
    }

    let status = "good";
    let text = `${duration.toFixed(0)}ms`;

    if (duration > 2000) {
      status = "critical";
      text = `SLOW: ${text}`;
    } else if (duration > 1000) {
      status = "warning";
      text = `SLOW: ${text}`;
    }

    indicator.textContent = text;
    indicator.className = `performance-indicator visible ${status}`;

    // Hide after 3 seconds
    setTimeout(() => {
      indicator.classList.remove("visible");
    }, 3000);
  }

  /**
   * Enhanced fetch with proper Odoo authentication + auto-retry on network errors
   */
  async enhancedFetch(route, params, options = {}) {
    const maxRetries = options.retries || 3;
    const retryDelay = options.retryDelay || 1000; // ms – will be multiplied by attempt index

    // === gather CSRF token (unchanged) ===
    let csrfToken = null;
    const metaToken = document
      .querySelector('meta[name="csrf-token"]')
      ?.getAttribute("content");
    if (metaToken) csrfToken = metaToken;
    if (!csrfToken) {
      const hiddenToken = document.querySelector(
        'input[name="csrf_token"]'
      )?.value;
      if (hiddenToken) csrfToken = hiddenToken;
    }
    if (!csrfToken) {
      csrfToken =
        window.odoo?.csrf_token ||
        window.csrf_token ||
        window.odoo?.__session_info__?.csrf_token;
    }

    const headers = {
      "Content-Type": "application/json",
      "X-Requested-With": "XMLHttpRequest",
    };
    if (csrfToken) headers["X-CSRFToken"] = csrfToken;

    const fetchBody = JSON.stringify({
      jsonrpc: "2.0",
      method: "call",
      params: params,
      id: Date.now(),
    });

    // Helper to actually perform the fetch once
    const doFetch = async () => {
      const resp = await fetch(route, {
        method: "POST",
        headers,
        credentials: "include",
        body: fetchBody,
      });
      if (!resp.ok) {
        throw new Error(`HTTP ${resp.status}: ${resp.statusText}`);
      }
      const data = await resp.json();
      if (data.error) {
        // Session expired special handling
        if (data.error.data?.name === "odoo.http.SessionExpiredException") {
          this.handleSessionExpired();
          throw new Error("Session expired. Please refresh the page.");
        }
        throw new Error(data.error.message || "RPC Error");
      }
      return data.result;
    };

    // Retry loop
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await doFetch();
      } catch (err) {
        // Only retry on network-level failures (TypeError / Failed to fetch)
        const isNetworkErr =
          err.name === "TypeError" || /Failed to fetch/i.test(err.message);
        if (!isNetworkErr || attempt === maxRetries - 1) {
          // out of retries or not network error ⇒ rethrow
          throw err;
        }
        console.warn(
          `🌐 Network error when calling ${route}. Retrying ${
            attempt + 1
          }/${maxRetries} ...`
        );
        await new Promise((res) => setTimeout(res, retryDelay * (attempt + 1))); // exponential-ish delay
      }
    }
  }

  /**
   * Initialize widget with enhanced loading and dashboard
   */
  async initializeWidget() {
    try {
      console.log("🚀 Đang khởi tạo SBot Chat Widget...");

      // Initialize services if not already done
      this.initializeServices();

      // Bind methods after services are ready
      this.bindMethods();

      // Disable body scroll for fixed full screen
      document.body.classList.add("sbotchat-open");
      document.documentElement.style.overflow = "hidden";
      document.body.style.overflow = "hidden";

      // Show loading state
      this.state.isLoading = true;

      // Load configuration and conversations in parallel
      await Promise.all([this.loadConfig(), this.loadConversations()]);

      // 🚀 PHASE 1: Initialize Redis Cache System
      await this.initializeRedisCache();

      // Setup UI enhancements
      this.setupUIEnhancements();

      // Initialize AI Response Formatter
      await this.initializeAIFormatter();

      // Initialize notification system
      this.initializeNotificationSystem();

      // Make widget globally accessible for form interactions
      window.sbotchatWidget = this;

      // Initialize performance monitoring
      this.initializePerformanceMonitoring();

      console.log("✅ SBot Chat Widget đã khởi tạo thành công");
    } catch (error) {
      console.error("❌ Không thể khởi tạo widget:", error);
      this.showNotification("Không thể khởi tạo giao diện chat", "danger");
    } finally {
      this.state.isLoading = false;
    }
  }

  /**
   * Initialize services with enhanced fallback and native Odoo integration
   */
  initializeServices() {
    // Try to get native Odoo RPC service first
    if (window.odoo && window.odoo.define) {
      try {
        // Try to access Odoo's RPC service through the service registry
        const serviceRegistry = window.odoo.__DEBUG__?.services;
        if (serviceRegistry?.rpc) {
          console.log("✅ Using native Odoo RPC service");
          this.rpc = serviceRegistry.rpc;
        }
        if (serviceRegistry?.orm) {
          this.orm = serviceRegistry.orm;
        }
        if (serviceRegistry?.user) {
          this.user = serviceRegistry.user;
        }
        if (serviceRegistry?.notification) {
          this.notification = serviceRegistry.notification;
        }
        if (serviceRegistry?.action) {
          this.action = serviceRegistry.action;
        }
      } catch (error) {
        console.warn("⚠️ Could not access native Odoo services:", error);
      }
    }

    // Fallback to useServiceSafe if native services not available
    if (!this.rpc) {
      this.rpc = this.useServiceSafe("rpc");
    }

    // Ensure rpcCall is always available as fallback
    if (!this.rpcCall) {
      this.rpcCall = this.rpcCall.bind(this);
    }
    if (!this.orm) {
      this.orm = this.useServiceSafe("orm");
    }
    if (!this.user) {
      this.user = this.useServiceSafe("user");
    }
    if (!this.notification) {
      this.notification = this.useServiceSafe("notification");
    }
    if (!this.action) {
      this.action = this.useServiceSafe("action");
    }

    console.log("🔧 Services initialized:", {
      rpc: !!this.rpc,
      orm: !!this.orm,
      user: !!this.user,
      notification: !!this.notification,
      action: !!this.action,
    });
  }

  /**
   * Bind all methods to ensure proper context
   */
  bindMethods() {
    // Essential methods
    this.sendMessage = this.sendMessage.bind(this);
    this.openSettings = this.openSettings.bind(this);
    this.closeSettings = this.closeSettings.bind(this);
    this.closeSettingsOnBackdrop = this.closeSettingsOnBackdrop.bind(this);
    this.saveConfig = this.saveConfig.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.selectConversation = this.selectConversation.bind(this);
    this.sendSuggestion = this.sendSuggestion.bind(this);
    this.insertText = this.insertText.bind(this);
    this.clearMessages = this.clearMessages.bind(this);
    this.toggleWebSearch = this.toggleWebSearch.bind(this);
    this.updateTemperatureDisplay = this.updateTemperatureDisplay.bind(this);
    this.resetSettings = this.resetSettings.bind(this);
    this.closeChat = this.closeChat.bind(this);
    this.createNewConversation = this.createNewConversation.bind(this);

    // Conversation menu methods
    this.toggleConversationMenu = this.toggleConversationMenu.bind(this);
    this.renameConversation = this.renameConversation.bind(this);
    this.duplicateConversation = this.duplicateConversation.bind(this);
    this.deleteConversation = this.deleteConversation.bind(this);

    // Dashboard methods
    // (Dashboard method bindings removed)
  }

  /**
   * Initialize AI Formatter with DeepSeek integration
   */
  async initializeAIFormatter() {
    try {
      // Check if AIResponseFormatter is available
      if (typeof window.AIResponseFormatter === "function") {
        this.aiFormatter = new window.AIResponseFormatter();

        // Pass current config to formatter for DeepSeek integration
        if (this.state.config) {
          window.sbotchatConfig = this.state.config;
        }

        console.log(
          "✅ AI Response Formatter initialized with DeepSeek integration"
        );

        // Setup formatted messages after formatter is ready
        setTimeout(() => this.setupFormattedMessages(), 500);
        return true;
      } else {
        console.warn("⚠️ AIResponseFormatter not found, will retry...");
        // Retry after a short delay
        setTimeout(() => this.initializeAIFormatter(), 1000);
        return false;
      }
    } catch (error) {
      console.error("❌ Failed to initialize AI formatter:", error);
      // Set fallback formatter
      this.aiFormatter = {
        formatResponse: (content) => this.fallbackFormatMessage(content),
      };
      return false;
    }
  }

  /**
   * Setup formatted messages with async support
   */
  setupFormattedMessages() {
    if (!this.aiFormatter) return;

    // Setup MutationObserver for dynamic message formatting
    if (this.formatObserver) {
      this.formatObserver.disconnect();
    }

    this.formatObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.formatMessagesInNode(node);
            }
          });
        }
      });
    });

    // Start observing
    const messagesContainer = document.querySelector(".sbotchat-messages");
    if (messagesContainer) {
      this.formatObserver.observe(messagesContainer, {
        childList: true,
        subtree: true,
      });

      // Format existing messages
      this.formatAllMessages();
    }
  }

  /**
   * Format all existing messages (async)
   */
  async formatAllMessages() {
    const messages = document.querySelectorAll(
      ".assistant-message .ai-formatted-content"
    );
    for (const messageElement of messages) {
      await this.formatMessageElement(messageElement);
    }
  }

  /**
   * Format messages in a specific node (async)
   */
  async formatMessagesInNode(node) {
    if (
      node.matches &&
      node.matches(".assistant-message .ai-formatted-content")
    ) {
      await this.formatMessageElement(node);
    } else {
      const messages = node.querySelectorAll
        ? node.querySelectorAll(".assistant-message .ai-formatted-content")
        : [];
      for (const messageElement of messages) {
        await this.formatMessageElement(messageElement);
      }
    }
  }

  /**
   * Format individual message element with async DeepSeek integration
   */
  async formatMessageElement(element) {
    if (!element || element.dataset.formatted === "true") return;

    try {
      const content = element.dataset.content;
      const hrAction = element.dataset.hrAction;
      const apiCalled = element.dataset.apiCalled;
      const intent = element.dataset.intent;

      if (!content) return;

      // Prepare data object for HR actions
      const data = hrAction
        ? {
            hr_action: hrAction === "true",
            api_called: apiCalled,
            intent: intent,
          }
        : null;

      // Show loading indicator for longer operations
      const originalContent = element.innerHTML;
      const isLongContent = content.length > 200;

      if (isLongContent) {
        element.innerHTML = `
                    <div class="ai-formatting-loader">
                        <div class="formatting-spinner"></div>
                        <span>AI đang phân tích và format nội dung...</span>
                    </div>
                `;
      }

      // Format with AI (async)
      let formattedContent;
      if (
        this.aiFormatter &&
        typeof this.aiFormatter.formatResponse === "function"
      ) {
        try {
          formattedContent = await this.aiFormatter.formatResponse(
            content,
            "assistant",
            data
          );
        } catch (error) {
          console.warn("AI formatting failed, using fallback:", error);
          formattedContent = this.fallbackFormatMessage(content);
        }
      } else {
        formattedContent = this.fallbackFormatMessage(content);
      }

      // Apply formatted content
      element.innerHTML = formattedContent;
      element.dataset.formatted = "true";
    } catch (error) {
      console.error("Error formatting message:", error);
      // Restore original content on error
      element.innerHTML = this.fallbackFormatMessage(
        element.dataset.content || ""
      );
    }
  }

  /**
   * Fallback formatting when AI Response Formatter is not available
   */
  fallbackFormatMessage(content) {
    if (!content) return "";

    // Basic markdown-like formatting
    let formatted = content;

    // Headers
    formatted = formatted.replace(
      /^### (.+)$/gm,
      '<h3 style="color: var(--primary-color); margin: 12px 0 8px 0;">$1</h3>'
    );
    formatted = formatted.replace(
      /^## (.+)$/gm,
      '<h2 style="color: var(--primary-color); margin: 14px 0 10px 0; border-left: 4px solid var(--primary-color); padding-left: 12px;">$1</h2>'
    );
    formatted = formatted.replace(
      /^# (.+)$/gm,
      '<h1 style="color: var(--primary-color); margin: 16px 0 12px 0; border-bottom: 2px solid var(--primary-color); padding-bottom: 8px;">$1</h1>'
    );

    // Bold and italic
    formatted = formatted.replace(
      /\*\*(.*?)\*\*/g,
      '<strong style="color: var(--text-primary);">$1</strong>'
    );
    formatted = formatted.replace(
      /\*(.*?)\*/g,
      '<em style="color: var(--text-secondary);">$1</em>'
    );

    // Inline code
    formatted = formatted.replace(
      /`([^`]+)`/g,
      '<code style="background: rgba(255,255,255,0.1); padding: 2px 6px; border-radius: 4px; font-family: monospace; color: #f59e0b;">$1</code>'
    );

    // Lists
    formatted = formatted.replace(
      /^[*-] (.+)$/gm,
      '<li style="margin: 6px 0; color: var(--text-primary);">$1</li>'
    );
    formatted = formatted.replace(
      /(<li>.*<\/li>)/s,
      '<ul style="margin: 12px 0; padding-left: 20px;">$1</ul>'
    );

    // Line breaks
    formatted = formatted.replace(
      /\n\n/g,
      '</p><p style="margin: 12px 0; color: var(--text-primary);">'
    );
    formatted = formatted.replace(/\n/g, "<br>");

    // Wrap in paragraph if needed
    if (
      !formatted.includes("<p>") &&
      !formatted.includes("<div>") &&
      !formatted.includes("<h")
    ) {
      formatted = `<p style="margin: 12px 0; color: var(--text-primary);">${formatted}</p>`;
    }

    return `<div style="background: rgba(255,255,255,0.02); border-radius: 8px; padding: 16px; border: 1px solid rgba(255,255,255,0.1);">${formatted}</div>`;
  }

  /**
   * Setup UI enhancements and interactions
   */
  setupUIEnhancements() {
    // Add keyboard shortcuts
    document.addEventListener("keydown", (e) => {
      // Ctrl/Cmd + K to focus message input
      if ((e.ctrlKey || e.metaKey) && e.key === "k") {
        e.preventDefault();
        const input = document.getElementById("message-input");
        if (input) input.focus();
      }

      // Escape to close settings
      if (e.key === "Escape" && this.state.showSettings) {
        this.closeSettings();
      }

      // Prevent page scroll with arrow keys
      if (
        ["ArrowUp", "ArrowDown", "PageUp", "PageDown", "Home", "End"].includes(
          e.key
        )
      ) {
        const target = e.target;
        // Không chặn khi focus ở input/textarea hoặc bên trong notification panel
        if (
          !target.matches("input, textarea, [contenteditable]") &&
          !target.closest(".notification-panel, .notification-list")
        ) {
          e.preventDefault();
        }
      }
    });

    // Prevent page scroll
    document.addEventListener(
      "wheel",
      (e) => {
        // Allow scrolling inside chat lists, modal bodies, AND right-panel dynamic form areas
        const target = e.target.closest(
          ".sbotchat-messages, .sbotchat-conversations, .sbotchat-modal-body, .form-container, .form-content, .notification-panel, .notification-list"
        );
        if (!target) {
          e.preventDefault();
        }
      },
      { passive: false }
    );

    // Setup auto-resize for input
    const messageInput = document.getElementById("message-input");
    if (messageInput) {
      messageInput.addEventListener("input", this.handleInputResize.bind(this));
      messageInput.addEventListener("input", this.handleUserTyping.bind(this));
    }

    // Setup suggestion chip hover effects
    this.setupSuggestionChips();

    // Setup quick action buttons
    this.setupQuickActions();

    // Ensure fixed positioning
    this.ensureFixedPositioning();

    // Setup New Chat button event listener
    this.setupNewChatButton();
  }

  /**
   * Setup New Chat button event listener
   */
  setupNewChatButton() {
    setTimeout(() => {
      const newChatBtn = document.getElementById("new-chat-btn");
      if (newChatBtn) {
        newChatBtn.addEventListener("click", this.createNewConversation);
        console.log("✅ New Chat button đã được thiết lập");
      }
    }, 100);
  }

  /**
   * Create new conversation
   */
  async createNewConversation() {
    try {
      console.log("🆕 Đang tạo cuộc trò chuyện mới...");

      // Prevent multiple concurrent creation
      if (this.state.isCreatingConversation) {
        console.log("⚠️ Đang tạo cuộc trò chuyện, vui lòng đợi...");
        return;
      }

      this.state.isCreatingConversation = true;

      // Generate title with current time
      const now = new Date();
      const title = `Chat ${now.toLocaleTimeString("vi-VN", {
        hour: "2-digit",
        minute: "2-digit",
      })}`;

      // Call create conversation endpoint
      const response = await this.rpc("/sbotchat/create_conversation", {
        title: title,
      });

      if (response && response.success) {
        console.log(
          "✅ Cuộc trò chuyện mới đã được tạo:",
          response.conversation
        );

        // Update conversations list
        await this.loadConversations();

        // Select the new conversation
        await this.selectConversation(response.conversation_id);

        // Clear current messages to show empty state
        this.state.messages = [];

        // Focus on input
        setTimeout(() => {
          const messageInput = document.getElementById("message-input");
          if (messageInput) {
            messageInput.focus();
          }
        }, 100);

        this.showNotification("Cuộc trò chuyện mới đã được tạo!", "success");
      } else {
        throw new Error(response?.error || "Không thể tạo cuộc trò chuyện");
      }
    } catch (error) {
      console.error("❌ Không thể tạo cuộc trò chuyện mới:", error);
      this.showNotification(
        `Không thể tạo cuộc trò chuyện: ${error.message}`,
        "danger"
      );
    } finally {
      this.state.isCreatingConversation = false;
    }
  }

  /**
   * Ensure fixed positioning and prevent scroll
   */
  ensureFixedPositioning() {
    const container = document.querySelector(".sbotchat-container");
    if (container) {
      container.style.position = "fixed";
      container.style.top = "0";
      container.style.left = "0";
      container.style.right = "0";
      container.style.bottom = "0";
      container.style.width = "100vw";
      container.style.height = "100vh";
      container.style.overflow = "hidden";
      container.style.zIndex = "1000";
    }

    // Prevent body scroll
    document.body.style.overflow = "hidden";
    document.documentElement.style.overflow = "hidden";

    // Handle window resize
    window.addEventListener("resize", () => {
      this.ensureFixedPositioning();
    });
  }

  /**
   * Handle input auto-resize
   */
  handleInputResize(event) {
    const input = event.target;
    input.style.height = "auto";
    input.style.height = Math.min(input.scrollHeight, 120) + "px";
  }

  /**
   * Handle user typing detection with performance optimization
   */
  handleUserTyping() {
    this.state.userTyping = true;

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    // Debounced typing detection
    this.typingTimeout = setTimeout(() => {
      this.state.userTyping = false;
    }, 1000);

    // Performance optimization for input
    this.optimizeInputPerformance();
  }

  /**
   * Debounce utility for performance optimization
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * Optimize input performance
   */
  optimizeInputPerformance() {
    const input = document.getElementById("message-input");
    if (!input) return;

    // Use requestAnimationFrame for smooth updates
    requestAnimationFrame(() => {
      // Add performance optimization classes
      input.classList.add("optimized-input");

      // Optimize text rendering
      input.style.textRendering = "optimizeSpeed";
      input.style.fontSmooth = "auto";
    });
  }

  /**
   * Setup suggestion chips with hover effects
   */
  setupSuggestionChips() {
    // Load HR suggestions dynamically
    // HR suggestions disabled in new simplified flow
    // this.loadHRSuggestions();

    // Original suggestion setup
    const suggestionContainer = document.querySelector(".sbotchat-suggestions");
    if (suggestionContainer) {
      suggestionContainer.addEventListener("click", (e) => {
        if (e.target.classList.contains("suggestion-chip")) {
          const suggestion = e.target.textContent;
          this.sendSuggestion(suggestion);
        }
      });
    }
  }

  /**
   * Setup quick action buttons
   */
  setupQuickActions() {
    const quickActionBtns = document.querySelectorAll(".quick-action-btn");
    quickActionBtns.forEach((btn) => {
      btn.addEventListener("click", (e) => {
        e.preventDefault();
        const text = btn.textContent.trim();
        console.log(`Quick action clicked: ${text}`);

        // Add visual feedback
        btn.style.transform = "scale(0.95)";
        setTimeout(() => {
          btn.style.transform = "scale(1)";
        }, 100);
      });
    });
  }

  /**
   * Enhanced config loading with better error handling
   */
  async loadConfig() {
    try {
      console.log("📋 Đang tải cấu hình...");

      const response = await this.rpcCall("/sbotchat/config", {});

      if (response && !response.error) {
        // Handle both direct config data and wrapped response
        const configData = response.data || response;
        this.state.config = { ...this.state.config, ...configData };

        console.log("✅ Cấu hình đã tải thành công:", {
          has_api_key: this.state.config.has_api_key,
          model_type: this.state.config.model_type,
        });

        // Update UI elements
        this.updateConfigUI();
      } else {
        console.warn("⚠️ Tải cấu hình trả về lỗi:", response?.error);
        this.state.config.has_api_key = false;

        // Try to create default config
        await this.createDefaultConfig();
      }
    } catch (error) {
      console.error("❌ Không thể tải cấu hình:", error);
      this.state.config.has_api_key = false;
      this.showNotification("Không thể tải cấu hình", "warning");
    }
  }

  /**
   * Create default config if none exists
   */
  async createDefaultConfig() {
    try {
      console.log("🔧 Đang tạo cấu hình mặc định...");

      const response = await this.rpc("/sbotchat/config", {
        name: "Cấu hình mặc định",
        model_type: "gpt-4o-mini",
        temperature: 1.0,
        max_tokens: 4000,
        rate_limit_per_hour: 100,
        system_prompt: "Bạn là một trợ lý AI hữu ích.",
        allow_delete: false,
      });

      if (response && response.success) {
        this.state.config = { ...this.state.config, ...response.data };
        console.log("✅ Cấu hình mặc định đã được tạo thành công");
      }
    } catch (error) {
      console.error("❌ Không thể tạo cấu hình mặc định:", error);
    }
  }

  /**
   * Update config UI elements
   */
  updateConfigUI() {
    setTimeout(() => {
      // Update form fields
      const apiKeyInput = document.getElementById("api-key");
      const modelSelect = document.getElementById("model-type");
      const temperatureInput = document.getElementById("temperature");
      const maxTokensInput = document.getElementById("max-tokens");
      const systemPromptTextarea = document.getElementById("system-prompt");
      const rateLimitInput = document.getElementById("rate-limit");
      const allowDeleteInput = document.getElementById("allow-delete");

      if (apiKeyInput && this.state.config.api_key) {
        apiKeyInput.value =
          this.state.config.api_key === "••••••••"
            ? ""
            : this.state.config.api_key;
      }

      // Update Brave Search API key
      const braveApiKeyInput = document.getElementById("brave-search-api-key");
      if (braveApiKeyInput && this.state.config.brave_search_api_key) {
        braveApiKeyInput.value =
          this.state.config.brave_search_api_key === "••••••••"
            ? ""
            : this.state.config.brave_search_api_key;
      }

      if (modelSelect) {
        modelSelect.value = this.state.config.model_type || "gpt-4o-mini";
      }

      if (temperatureInput) {
        temperatureInput.value = this.state.config.temperature || 1.0;
        this.updateTemperatureDisplay();
      }

      if (maxTokensInput) {
        maxTokensInput.value = this.state.config.max_tokens || 4000;
      }

      if (systemPromptTextarea) {
        systemPromptTextarea.value =
          this.state.config.system_prompt ||
          "Bạn là một trợ lý AI hữu ích. Hãy ngắn gọn và chính xác trong các phản hồi của bạn.";
      }

      if (rateLimitInput) {
        rateLimitInput.value = this.state.config.rate_limit_per_hour || 100;
      }

      if (allowDeleteInput) {
        allowDeleteInput.checked = !!this.state.config.allow_delete;
      }

      // Update web search settings
      const enableWebSearchInput = document.getElementById("enable-web-search");
      if (enableWebSearchInput) {
        enableWebSearchInput.checked = !!this.state.config.enable_web_search;
        // Also update the toggle state
        this.state.webSearchEnabled = !!this.state.config.enable_web_search;
      }
    }, 100);
  }

  /**
   * Load conversations with enhanced UI
   */
  async loadConversations() {
    try {
      console.log("💬 Đang tải cuộc trò chuyện...");

      const response = await this.rpcCall("/sbotchat/conversations", {});

      if (response && response.success) {
        this.state.conversations = response.data || [];
        console.log(
          `✅ Đã tải ${this.state.conversations.length} cuộc trò chuyện`
        );

        // Auto-select first conversation if available and no current conversation
        if (
          this.state.conversations.length > 0 &&
          !this.state.currentConversationId
        ) {
          await this.selectConversation(this.state.conversations[0].id);
        }
      }
    } catch (error) {
      console.error("❌ Không thể tải cuộc trò chuyện:", error);
      this.showNotification("Không thể tải cuộc trò chuyện", "warning");
    }
  }

  /**
   * Send message to AI
   */
  async sendMessage() {
    const messageInput = document.getElementById("message-input");
    if (!messageInput) return;

    const message = messageInput.value.trim();
    if (!message) return;

    // Clear input and show user message immediately
    messageInput.value = "";
    this.handleInputResize({ target: messageInput });

    const userMessage = {
      id: ++this.state.lastMessageId,
      role: "user",
      content: message,
      timestamp: new Date(),
      isLocal: true,
    };

    this.state.messages.push(userMessage);
    this.scrollToBottom();

    // Show typing indicator
    this.state.isTyping = true;
    this.state.connectionStatus = "thinking";

    try {
      // Send message directly (synchronous processing for immediate response)
      const response = await this.rpcCall("/sbotchat/chat_with_openai", {
        message: message,
        conversation_id: this.state.currentConversationId,
        enable_web_search: this.state.webSearchEnabled, // Web search toggle
      });

      if (response.error) {
        throw new Error(response.error);
      }

      if (response.success) {
        // Update conversation ID if new conversation was created
        if (response.conversation_id && !this.state.currentConversationId) {
          this.state.currentConversationId = response.conversation_id;
          await this.loadConversations(); // Refresh conversation list
        }

        // Direct processing mode (synchronous response)
        const assistantMessage = {
          id: ++this.state.lastMessageId,
          role: "assistant",
          content: response.response || "Không có phản hồi",
          thinking: response.thinking || null,
          timestamp: new Date(),
          model_used: response.model_used || "unknown",
          tokens_used: response.tokens_used || 0,
          response_time: response.response_time || 0,
          hr_action: response.hr_action || false,
          api_called: response.api_called || null,
          intent: response.intent || null,
          isLocal: true,
        };

        this.state.messages.push(assistantMessage);

        // Show HR-specific UI enhancements
        if (response.hr_action) {
          this.showHRActionFeedback(response);
        }

        // Show success notification with enhanced info
        let notificationMsg = "Phản hồi nhận được";
        if (response.hr_action) {
          notificationMsg = "🤖 HR AI Agent đã xử lý yêu cầu";
        } else if (response.tokens_used) {
          notificationMsg = `✅ Sử dụng ${response.tokens_used} tokens`;
        }

        this.showNotification(notificationMsg, "success");

        // Format the new message after a short delay to ensure DOM is updated
        setTimeout(() => {
          this.formatAllMessages();
          this.scrollToBottom();
        }, 100);

        this.scrollToBottom();
      }
    } catch (error) {
      console.error("Lỗi gửi tin nhắn:", error);

      // Add error message to chat
      const errorMessage = {
        id: ++this.state.lastMessageId,
        role: "assistant",
        content: `❌ **Lỗi:** ${error.message}\n\n💡 **Gợi ý:**\n- Kiểm tra kết nối internet\n- Xác nhận khóa API OpenAI\n- Thử lại sau vài giây`,
        timestamp: new Date(),
        isError: true,
        isLocal: true,
      };

      this.state.messages.push(errorMessage);
      this.showNotification("Không thể gửi tin nhắn", "danger");
    } finally {
      this.state.isTyping = false;
      this.state.connectionStatus = "online";

      // Check Redis status after sending message
      this.checkRedisStatus().catch((err) => {
        console.warn("Redis status check failed:", err);
      });
    }
  }

  /**
   * Setup listener for pending request completion
   */
  setupPendingRequestListener(pendingRequestId) {
    try {
      // Subscribe to bus notifications for this user
      const userId = odoo.session_info?.uid || 1;
      const channel = `sbotchat_channel_${userId}`;

      // Add listener for this specific request
      if (this.bus && typeof this.bus.subscribe === "function") {
        this.bus.subscribe(channel, (notification) => {
          this.handlePendingRequestUpdate(notification, pendingRequestId);
        });
      }
    } catch (error) {
      console.warn("Không thể setup pending request listener:", error);
    }
  }

  /**
   * Handle pending request updates from bus
   */
  handlePendingRequestUpdate(notification, targetRequestId) {
    try {
      const {
        type,
        request_id,
        title,
        message: msg,
        conversation_id,
      } = notification;

      // Only handle notifications for our target request
      if (request_id !== targetRequestId) return;

      if (type === "response_ready") {
        // Find the pending message and update it
        const pendingMessageIndex = this.state.messages.findIndex(
          (m) => m.isPending && m.pendingRequestId === request_id
        );

        if (pendingMessageIndex !== -1) {
          // Remove pending message
          this.state.messages.splice(pendingMessageIndex, 1);

          // Reload conversation to get the actual response
          if (conversation_id === this.state.currentConversationId) {
            this.loadConversationMessages();
          }
        }

        this.showNotification(
          "✅ SBot đã trả lời! Kiểm tra cuộc trò chuyện.",
          "success"
        );
      } else if (type === "response_error") {
        // Find the pending message and replace with error
        const pendingMessageIndex = this.state.messages.findIndex(
          (m) => m.isPending && m.pendingRequestId === request_id
        );

        if (pendingMessageIndex !== -1) {
          this.state.messages[pendingMessageIndex] = {
            ...this.state.messages[pendingMessageIndex],
            content: `❌ **Lỗi xử lý:** ${notification.error}\n\n💡 Vui lòng thử lại sau.`,
            isPending: false,
            isError: true,
          };
        }

        this.showNotification("❌ Có lỗi khi xử lý tin nhắn", "danger");
      }

      // Refresh UI
      setTimeout(() => {
        this.formatAllMessages();
        this.scrollToBottom();
      }, 100);
    } catch (error) {
      console.error("Lỗi xử lý pending request update:", error);
    }
  }

  /**
   * Load messages for current conversation
   */
  async loadConversationMessages() {
    if (!this.state.currentConversationId) return;

    try {
      const response = await this.rpcCall(
        "/sbotchat/get_conversation_messages",
        {
          conversation_id: this.state.currentConversationId,
        }
      );

      if (response.success && response.messages) {
        // Convert backend messages to frontend format
        this.state.messages = response.messages.map((msg, index) => ({
          id: index + 1,
          role: msg.role,
          content: msg.content,
          thinking: msg.thinking_content,
          timestamp: new Date(msg.create_date),
          model_used: msg.model_used,
          tokens_used: msg.tokens_used,
          response_time: msg.response_time,
          isLocal: false,
        }));

        this.state.lastMessageId = this.state.messages.length;

        setTimeout(() => {
          this.formatAllMessages();
          this.scrollToBottom();
        }, 100);
      }
    } catch (error) {
      console.error("Lỗi load conversation messages:", error);
    }
  }

  /**
   * Email validation helper
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Phone validation helper
   */
  isValidPhone(phone) {
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,15}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Clean up empty text nodes and excessive whitespace
   */
  cleanupTextNodes(element) {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    const textNodes = [];
    let node;

    // Collect all text nodes
    while ((node = walker.nextNode())) {
      textNodes.push(node);
    }

    // Clean up text nodes
    textNodes.forEach((textNode) => {
      const content = textNode.textContent;

      // If text node contains only \n or whitespace, remove or clean it
      if (content.match(/^[\s\n\\n]+$/)) {
        // If it's just whitespace/newlines, remove it
        textNode.remove();
      } else if (content.includes("\\n")) {
        // Replace literal \n with single space
        textNode.textContent = content
          .replace(/\\n/g, " ")
          .replace(/\s+/g, " ")
          .trim();
      }
    });
  }

  /**
   * Setup enhanced form submission for right-panel
   */
  setupEnhancedFormSubmission(formWrapper, targetModel, originalMessage) {
    const form = formWrapper.querySelector("form");
    if (!form) return;

    form.addEventListener("submit", async (e) => {
      e.preventDefault();

      // Show inline loading state
      this.showInlineFormLoading(form, true);

      try {
        // Collect data only to send to chat (no backend)
        const formData = new FormData(form);
        const data = this.processFormDataForRightPanel(formData);

        // Build lines to display
        const lines = [];
        Object.entries(data).forEach(([key, val]) => {
          if (Array.isArray(val)) {
            lines.push(`📝 ${key}: ${val.join(", ")}`);
          } else {
            lines.push(`📝 ${key}: ${val}`);
          }
        });

        if (lines.length === 0) {
          this.showInlineFormError(form, "Không có dữ liệu để gửi");
          return;
        }

        const autoMessage = `📋 **Thông tin form (${
          targetModel || "model?"
        })**\n${lines.join("\n")}`;

        const messageInput = this.el
          ? this.el.querySelector("#message-input")
          : document.querySelector("#message-input");
        if (messageInput) {
          messageInput.value = autoMessage;
          setTimeout(() => {
            this.sendMessage().catch((err) => {
              console.warn("Auto send failed:", err);
              this.showInlineFormError(form, "Gửi tin thất bại");
            });
          }, 300);
        } else {
          this.showInlineFormError(form, "Không tìm thấy ô chat");
        }

        // Visual feedback
        this.showInlineFormLoading(form, false);
        this.showCompactFormSuccess(form, { record_id: "🡒 Đã gửi qua chat" });

        setTimeout(() => form.reset(), 1500);
      } catch (error) {
        console.error("Form submission error:", error);
        this.showInlineFormError(form, error.message);
      } finally {
        this.showInlineFormLoading(form, false);
      }
    });

    // Setup enhanced form validation for right-panel
    this.setupCompactFormValidation(form);
  }

  /**
   * Setup infinite scroll optimization for form container
   */
  setupInfiniteScrollOptimization(formContainer) {
    // Ensure container can handle infinite scroll
    formContainer.style.overflowY = "auto";
    formContainer.style.overflowX = "hidden";
    formContainer.style.maxHeight = "calc(100vh - 80px)";

    // Add smooth scrolling
    formContainer.style.scrollBehavior = "smooth";

    // Optimize scroll performance
    formContainer.style.willChange = "scroll-position";
    formContainer.style.transform = "translateZ(0)"; // Hardware acceleration

    // Setup scroll event for performance monitoring
    let scrollTimeout;
    formContainer.addEventListener("scroll", () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        // Optimize rendering during scroll
        this.optimizeScrollPerformance(formContainer);
      }, 150);
    });
  }

  /**
   * Process form data specifically for right-panel submission
   */
  processFormDataForRightPanel(formData) {
    const data = {};

    for (let [key, value] of formData.entries()) {
      // Handle multi-select fields
      if (key.endsWith("_ids")) {
        if (!data[key]) {
          data[key] = [];
        }
        data[key].push(value);
      } else {
        // Clean and validate single values
        data[key] = value.trim();
      }
    }

    // Remove empty values to save space
    Object.keys(data).forEach((key) => {
      if (
        data[key] === "" ||
        (Array.isArray(data[key]) && data[key].length === 0)
      ) {
        delete data[key];
      }
    });

    return data;
  }

  /**
   * Show inline loading state for compact form
   */
  showInlineFormLoading(form, isLoading) {
    const submitBtn = form.querySelector('button[type="submit"]');
    if (!submitBtn) return;

    if (isLoading) {
      submitBtn.disabled = true;
      submitBtn.innerHTML =
        '<i class="fa fa-spinner fa-spin"></i> Đang xử lý...';
      submitBtn.style.opacity = "0.7";
    } else {
      submitBtn.disabled = false;
      submitBtn.innerHTML = '<i class="fa fa-check"></i> Tạo mới';
      submitBtn.style.opacity = "1";
    }
  }

  /**
   * Show compact success message in form
   */
  showCompactFormSuccess(form, response) {
    // Create compact success indicator
    const successDiv = document.createElement("div");
    successDiv.className = "alert alert-success alert-sm mb-2";
    successDiv.style.fontSize = "12px";
    successDiv.style.padding = "6px 10px";
    successDiv.innerHTML = `
            <i class="fa fa-check-circle text-success"></i>
            <strong>Thành công!</strong> ID: ${response.record_id}
        `;

    // Insert after form header
    const formContent = form.closest(".form-content");
    if (formContent) {
      formContent.insertBefore(successDiv, form);

      // Auto-remove after 3 seconds
      setTimeout(() => successDiv.remove(), 3000);
    }
  }

  /**
   * Generate compact chat summary for right-panel
   */
  generateCompactChatSummary(form, response, data) {
    // Build detailed summary of submitted data
    const lines = [];
    form.querySelectorAll(".form-group").forEach((group) => {
      const labelEl = group.querySelector("label");
      const inputEl = group.querySelector("input, textarea, select");
      if (!inputEl) return;

      const labelText = labelEl
        ? labelEl.textContent.trim().replace(/\s*\*$/, "")
        : inputEl.name || "";
      let valueText = "";

      if (inputEl.tagName === "SELECT") {
        if (inputEl.multiple) {
          valueText = [...inputEl.selectedOptions]
            .map((o) => o.textContent.trim())
            .join(", ");
        } else {
          const sel = inputEl.selectedOptions[0];
          valueText = sel ? sel.textContent.trim() : "";
        }
      } else if (inputEl.type === "checkbox") {
        valueText = inputEl.checked ? "Có" : "Không";
      } else if (inputEl.type === "radio") {
        if (inputEl.checked) {
          valueText = inputEl.value || "Đã chọn";
        } else {
          return; // Skip unchecked radio
        }
      } else {
        valueText = inputEl.value;
      }

      if (valueText && valueText.trim()) {
        lines.push(`📝 ${labelText}: ${valueText}`);
      }
    });

    // Create beautiful formatted message
    const modelDisplayName = response.model_display_name || response.model;
    const actionVerb =
      response.action === "update" ? "cập nhật" : "tạo thành công";
    let autoMessage = `✅ **Đã ${actionVerb} ${modelDisplayName}** (ID: ${response.record_id})\n\n`;

    if (lines.length > 0) {
      autoMessage += `**📋 Thông tin đã nhập:**\n${lines.join("\n")}\n\n`;
    }

    autoMessage += `💡 *Tin nhắn này được tạo tự động từ form*`;

    // Show notification about auto-message
    this.showNotification("📨 Đang gửi tin nhắn tự động...", "info");

    // Auto-send to chat
    const messageInput = this.el
      ? this.el.querySelector("#message-input")
      : document.querySelector("#message-input");
    if (messageInput) {
      messageInput.value = autoMessage;

      // Add a small delay for better UX
      setTimeout(() => {
        this.sendMessage()
          .then(() => {
            this.showNotification(
              "✅ Đã gửi tin nhắn tự động thành công!",
              "success"
            );
          })
          .catch((err) => {
            console.warn("Auto chat message failed:", err);
            this.showNotification(
              "⚠️ Không thể gửi tin nhắn tự động",
              "warning"
            );
          });
      }, 300); // Small delay for better UX
    }
  }

  /**
   * Setup compact form validation for right-panel
   */
  setupCompactFormValidation(form) {
    const inputs = form.querySelectorAll(
      "input[required], textarea[required], select[required]"
    );

    inputs.forEach((input) => {
      input.addEventListener("blur", () => {
        this.validateCompactField(input);
      });

      input.addEventListener("input", () => {
        // Clear error state immediately
        input.classList.remove("is-invalid");
        const feedback = input.nextElementSibling;
        if (feedback && feedback.classList.contains("invalid-feedback")) {
          feedback.remove();
        }
      });
    });
  }

  /**
   * Validate field with compact error display
   */
  validateCompactField(input) {
    let isValid = true;
    let errorMessage = "";

    if (input.hasAttribute("required") && !input.value.trim()) {
      isValid = false;
      errorMessage = "Bắt buộc";
    } else if (
      input.type === "email" &&
      input.value &&
      !this.isValidEmail(input.value)
    ) {
      isValid = false;
      errorMessage = "Email không hợp lệ";
    }

    // Show compact error
    if (!isValid) {
      input.classList.add("is-invalid");

      // Remove existing error
      const existingError = input.nextElementSibling;
      if (
        existingError &&
        existingError.classList.contains("invalid-feedback")
      ) {
        existingError.remove();
      }

      // Add compact error message
      const errorDiv = document.createElement("div");
      errorDiv.className = "invalid-feedback";
      errorDiv.style.fontSize = "11px";
      errorDiv.textContent = errorMessage;
      input.parentNode.insertBefore(errorDiv, input.nextSibling);
    } else {
      input.classList.remove("is-invalid");
    }

    return isValid;
  }

  /**
   * Show inline form error
   */
  showInlineFormError(form, message) {
    const errorDiv = document.createElement("div");
    errorDiv.className = "alert alert-danger alert-sm mb-2";
    errorDiv.style.fontSize = "12px";
    errorDiv.style.padding = "6px 10px";
    errorDiv.innerHTML = `<i class="fa fa-exclamation-triangle"></i> ${message}`;

    const formContent = form.closest(".form-content");
    if (formContent) {
      formContent.insertBefore(errorDiv, form);
      setTimeout(() => errorDiv.remove(), 5000);
    }
  }

  /**
   * Optimize scroll performance during form interaction with advanced techniques
   */
  optimizeScrollPerformance(container) {
    if (!container) return;

    // Add scroll performance optimizations
    container.style.willChange = "scroll-position";
    container.style.transform = "translateZ(0)"; // Hardware acceleration
    container.style.contain = "layout style paint"; // CSS containment

    // Enhanced scroll optimization with passive listeners
    let ticking = false;
    let lastScrollTime = 0;

    const optimizedScrollHandler = this.debounce(() => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const now = performance.now();

          // Only optimize if scrolling is active
          if (now - lastScrollTime > 100) {
            this.optimizeVisibleElements(container);
          }

          lastScrollTime = now;
          ticking = false;
        });
        ticking = true;
      }
    }, 16); // 60fps throttling

    // Use passive listener for better performance
    container.addEventListener("scroll", optimizedScrollHandler, {
      passive: true,
    });

    // Intersection Observer for lazy loading
    this.setupLazyLoading(container);
  }

  /**
   * Setup lazy loading with Intersection Observer
   */
  setupLazyLoading(container) {
    if (!window.IntersectionObserver) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("loaded");
            observer.unobserve(entry.target);
          }
        });
      },
      {
        root: container,
        rootMargin: "50px",
        threshold: 0.1,
      }
    );

    // Observe elements that can be lazy loaded
    container.querySelectorAll(".lazy-load").forEach((el) => {
      observer.observe(el);
    });

    this.lazyObserver = observer;
  }

  /**
   * Optimize visible elements during scroll
   */
  optimizeVisibleElements(container) {
    const visibleElements = container.querySelectorAll(
      ".message, .form-group, input, select, textarea"
    );

    visibleElements.forEach((el) => {
      const rect = el.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();

      // Check if element is visible
      const isVisible =
        rect.top < containerRect.bottom && rect.bottom > containerRect.top;

      if (isVisible) {
        el.classList.add("visible");
        // Enable animations for visible elements
        el.style.willChange = "transform, opacity";
      } else {
        el.classList.remove("visible");
        // Disable animations for non-visible elements
        el.style.willChange = "auto";
      }
    });
  }

  /**
   * Handle chat error
   */
  handleChatError(error) {
    console.error("Chat workflow error:", error);

    // Add error message to chat (no need to remove loading since we don't show it)
    const errorMessage = {
      id: ++this.state.lastMessageId,
      role: "assistant",
      content: `❌ **Lỗi kết nối:** ${
        error.message || "Không thể kết nối đến AI"
      }\n\n💡 **Thử lại:**\n- Kiểm tra kết nối internet\n- Xác nhận cấu hình API\n- Gửi lại tin nhắn`,
      timestamp: new Date(),
      isError: true,
      isLocal: true,
    };

    this.state.messages.push(errorMessage);
    this.scrollToBottom();

    // Show notification
    this.showNotification("Lỗi kết nối AI", "danger");
  }

  /**
   * Show HR Action feedback to user
   */
  showHRActionFeedback(response) {
    const feedbackContainer = document.querySelector(".sbotchat-hr-feedback");
    if (feedbackContainer) {
      const feedback = document.createElement("div");
      feedback.className = "hr-action-success";
      feedback.innerHTML = `
                <div class="hr-feedback-icon">🤖</div>
                <div class="hr-feedback-content">
                    <strong>HR AI Agent</strong>
                    <p>API gọi: ${response.api_called}</p>
                    <p>Intent: ${response.intent}</p>
                </div>
            `;
      feedbackContainer.appendChild(feedback);

      // Auto remove after 3 seconds
      setTimeout(() => {
        feedback.remove();
      }, 3000);
    }
  }

  /**
   * Send suggestion with smooth animation
   */
  async sendSuggestion(suggestion) {
    const messageInput = document.getElementById("message-input");
    if (messageInput) {
      messageInput.value = suggestion;
      messageInput.focus();

      // Animate the input
      messageInput.style.transform = "scale(1.02)";
      setTimeout(() => {
        messageInput.style.transform = "scale(1)";
      }, 150);

      // Auto-send after a brief delay
      setTimeout(() => {
        this.sendMessage();
      }, 300);
    }
  }

  /**
   * Insert text into input with cursor positioning
   */
  insertText(text) {
    const messageInput = document.getElementById("message-input");
    if (messageInput) {
      const cursorPos = messageInput.selectionStart;
      const currentValue = messageInput.value;
      const newValue =
        currentValue.slice(0, cursorPos) + text + currentValue.slice(cursorPos);

      messageInput.value = newValue;
      messageInput.focus();
      messageInput.setSelectionRange(
        cursorPos + text.length,
        cursorPos + text.length
      );

      // Trigger resize
      this.handleInputResize({ target: messageInput });
    }
  }

  /**
   * Clear messages with confirmation
   */
  async clearMessages() {
    if (this.state.messages.length === 0) {
      this.showNotification("Không có tin nhắn để xóa", "info");
      return;
    }

    if (
      confirm(
        "Bạn có chắc chắn muốn xóa tất cả tin nhắn trong cuộc trò chuyện này không?"
      )
    ) {
      this.state.messages = [];
      this.showNotification("Tin nhắn đã được xóa", "success");

      // Animate the clear action
      const messagesContainer = document.getElementById("messages-container");
      if (messagesContainer) {
        messagesContainer.style.opacity = "0.5";
        setTimeout(() => {
          messagesContainer.style.opacity = "1";
        }, 300);
      }
    }
  }

  /**
   * Toggle web search functionality
   */
  toggleWebSearch() {
    this.state.webSearchEnabled = !this.state.webSearchEnabled;

    const status = this.state.webSearchEnabled ? "BẬT" : "TẮT";
    const type = this.state.webSearchEnabled ? "success" : "info";

    this.showNotification(`Tìm kiếm web đã ${status}`, type);

    // Log for debugging
    console.log("🌐 Web Search Toggle:", {
      enabled: this.state.webSearchEnabled,
      message: `Tìm kiếm web đã ${status}`,
    });

    // Update input placeholder dynamically
    const input = document.getElementById("message-input");
    if (input) {
      if (this.state.webSearchEnabled) {
        input.setAttribute(
          "placeholder",
          "Nhập câu hỏi để tìm kiếm web và chat... (Enter để gửi)"
        );
      } else {
        input.setAttribute(
          "placeholder",
          "Nhập tin nhắn của bạn tại đây... (Nhấn Enter để gửi)"
        );
      }
    }
  }

  /**
   * Enhanced conversation selection
   */
  async selectConversation(conversationId) {
    try {
      console.log(`🔄 Đang chọn cuộc trò chuyện: ${conversationId}`);

      this.state.currentConversationId = conversationId;
      this.state.currentConversation = this.state.conversations.find(
        (c) => c.id === conversationId
      );

      // Load messages for this conversation
      const response = await this.rpc("/sbotchat/conversation_messages", {
        conversation_id: conversationId,
      });

      if (response && response.success) {
        this.state.messages = response.data.map((msg) => ({
          ...msg,
          timestamp: this.formatTimestamp(new Date(msg.timestamp)),
        }));

        // Format messages after a short delay to ensure DOM is updated
        setTimeout(() => {
          this.formatAllMessages();
          this.scrollToBottom();
        }, 200);

        console.log(`✅ Đã tải ${this.state.messages.length} tin nhắn`);
      }
    } catch (error) {
      console.error("❌ Không thể chọn cuộc trò chuyện:", error);
      this.showNotification("Không thể tải cuộc trò chuyện", "danger");
    }
  }

  /**
   * Enhanced settings management
   */
  openSettings() {
    this.state.showSettings = true;

    // Load current config into form
    setTimeout(() => {
      this.updateConfigUI();
    }, 100);

    // Add modal animation
    setTimeout(() => {
      const modal = document.querySelector(".sbotchat-modal");
      if (modal) {
        modal.style.opacity = "0";
        modal.style.transform = "scale(0.95)";
        setTimeout(() => {
          modal.style.opacity = "1";
          modal.style.transform = "scale(1)";
        }, 10);
      }
    }, 10);
  }

  closeSettings() {
    // Animate close
    const modal = document.querySelector(".sbotchat-modal");
    if (modal) {
      modal.style.opacity = "0";
      modal.style.transform = "scale(0.95)";
      setTimeout(() => {
        this.state.showSettings = false;
      }, 200);
    } else {
      this.state.showSettings = false;
    }
  }

  closeSettingsOnBackdrop(event) {
    if (event.target.classList.contains("sbotchat-modal")) {
      this.closeSettings();
    }
  }

  /**
   * Enhanced config saving with validation
   */
  async saveConfig() {
    try {
      const apiKey = document.getElementById("api-key")?.value;
      const braveSearchApiKey = document.getElementById(
        "brave-search-api-key"
      )?.value;
      const modelType = document.getElementById("model-type")?.value;
      const temperature = parseFloat(
        document.getElementById("temperature")?.value || 1.0
      );
      const maxTokens = parseInt(
        document.getElementById("max-tokens")?.value || 4000
      );
      const systemPrompt = document.getElementById("system-prompt")?.value;
      const rateLimit = parseInt(
        document.getElementById("rate-limit")?.value || 100
      );
      const allowDelete = document.getElementById("allow-delete")?.checked;
      const enableWebSearch =
        document.getElementById("enable-web-search")?.checked;

      // Enhanced validation
      if (apiKey && apiKey !== "••••••••") {
        if (!apiKey.startsWith("sk-")) {
          this.showNotification(
            "Định dạng khóa API không hợp lệ. Khóa API OpenAI phải bắt đầu bằng 'sk-'",
            "danger"
          );
          return;
        }

        if (apiKey.length < 20) {
          this.showNotification("Khóa API có vẻ quá ngắn", "warning");
          return;
        }
      }

      if (temperature < 0 || temperature > 2) {
        this.showNotification(
          "Nhiệt độ phải nằm trong khoảng từ 0 đến 2",
          "danger"
        );
        return;
      }

      if (maxTokens < 100 || maxTokens > 8000) {
        this.showNotification(
          "Số token tối đa phải nằm trong khoảng từ 100 đến 8000",
          "danger"
        );
        return;
      }

      if (rateLimit < 10 || rateLimit > 10000) {
        this.showNotification(
          "Giới hạn request mỗi giờ phải nằm trong khoảng 10 - 10000",
          "danger"
        );
        return;
      }

      // Nếu người dùng bật allowDelete lần đầu, hiển thị cảnh báo xác nhận
      if (allowDelete && !this.state.config.allow_delete) {
        const confirmMsg =
          "CẢNH BÁO: Việc cho phép xoá dữ liệu là hành động không thể hoàn tác. Bạn hoàn toàn chịu trách nhiệm về mọi mất mát dữ liệu phát sinh. Bạn có chắc chắn muốn tiếp tục?";
        if (!confirm(confirmMsg)) {
          return; // Huỷ lưu cấu hình nếu không đồng ý
        }
      }

      // Show saving state
      const saveBtn = document.querySelector(".btn-sbotchat");
      if (saveBtn) {
        // Store original text if not already stored
        if (!saveBtn.getAttribute("data-original-text")) {
          saveBtn.setAttribute("data-original-text", saveBtn.innerHTML);
        }
        saveBtn.innerHTML =
          '<i class="fa fa-spinner fa-spin" style="margin-right: 6px;"></i>Đang lưu...';
        saveBtn.disabled = true;
      }

      // Save configuration - sử dụng rpcCall thay vì rpc
      console.log(
        "💾 Đang lưu cấu hình với API key:",
        apiKey ? apiKey.substring(0, 10) + "..." : "none"
      );

      const response = await this.rpcCall("/sbotchat/config", {
        api_key: apiKey,
        brave_search_api_key: braveSearchApiKey,
        model_type: modelType,
        temperature: temperature,
        max_tokens: maxTokens,
        system_prompt: systemPrompt,
        rate_limit_per_hour: rateLimit,
        allow_delete: allowDelete,
        enable_web_search: enableWebSearch,
      });

      console.log("📨 Response từ server:", response);

      if (response && response.success) {
        // Update local state
        this.state.config = { ...this.state.config, ...response.data };

        console.log("✅ Cấu hình đã được lưu thành công!");
        this.showNotification("Cấu hình đã được lưu thành công!", "success");

        // Auto-close modal after success
        setTimeout(() => {
          this.closeSettings();
        }, 1000);
      } else {
        console.error("❌ Lỗi lưu cấu hình:", response);
        throw new Error(response?.error || "Không thể lưu cấu hình");
      }
    } catch (error) {
      console.error("❌ Không thể lưu cấu hình:", error);
      this.showNotification(
        `Không thể lưu cấu hình: ${error.message}`,
        "danger"
      );
    } finally {
      // Restore save button
      const saveBtn = document.querySelector(".btn-sbotchat");
      if (saveBtn) {
        const originalText =
          saveBtn.getAttribute("data-original-text") ||
          '<i class="fa fa-save" style="margin-right: 6px;"></i>Lưu cấu hình';
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
      }
    }
  }

  /**
   * Reset settings to default
   */
  resetSettings() {
    if (
      confirm(
        "Bạn có chắc chắn muốn đặt lại tất cả cài đặt về giá trị mặc định không?"
      )
    ) {
      // Reset form fields
      const apiKeyInput = document.getElementById("api-key");
      const modelSelect = document.getElementById("model-type");
      const temperatureInput = document.getElementById("temperature");
      const maxTokensInput = document.getElementById("max-tokens");
      const systemPromptTextarea = document.getElementById("system-prompt");
      const rateLimitInput = document.getElementById("rate-limit");
      const allowDeleteInput = document.getElementById("allow-delete");

      if (apiKeyInput) apiKeyInput.value = "";

      // Reset Brave Search API key
      const braveApiKeyInput = document.getElementById("brave-search-api-key");
      if (braveApiKeyInput) braveApiKeyInput.value = "";

      if (modelSelect) modelSelect.value = "gpt-4o-mini";
      if (temperatureInput) {
        temperatureInput.value = "1.0";
        this.updateTemperatureDisplay();
      }
      if (maxTokensInput) maxTokensInput.value = "4000";
      if (systemPromptTextarea) {
        systemPromptTextarea.value =
          "Bạn là một trợ lý AI hữu ích. Hãy ngắn gọn và chính xác trong các phản hồi của bạn.";
      }

      if (rateLimitInput) {
        rateLimitInput.value = "100";
      }

      if (allowDeleteInput) {
        allowDeleteInput.checked = false;
      }

      // Reset web search settings
      const enableWebSearchInput = document.getElementById("enable-web-search");
      if (enableWebSearchInput) {
        enableWebSearchInput.checked = false;
      }

      this.showNotification(
        "Cài đặt đã được đặt lại về giá trị mặc định",
        "info"
      );
    }
  }

  /**
   * Update temperature display
   */
  updateTemperatureDisplay() {
    const temperatureInput = document.getElementById("temperature");
    const temperatureValue = document.getElementById("temperature-value");

    if (temperatureInput && temperatureValue) {
      temperatureValue.textContent = parseFloat(temperatureInput.value).toFixed(
        1
      );
    }
  }

  /**
   * Enhanced keyboard handling
   */
  handleKeyDown(event) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }

    // Shift+Enter for new line
    if (event.key === "Enter" && event.shiftKey) {
      // Allow default behavior (new line)
      return;
    }
  }

  /**
   * Enhanced notification system
   */
  showNotification(message, type = "info") {
    try {
      if (this.notification && this.notification.add) {
        this.notification.add(message, { type: type });
      } else {
        // Fallback notification
        this.createCustomNotification(message, type);
      }
    } catch (error) {
      console.warn("Dịch vụ thông báo thất bại, sử dụng fallback:", error);
      this.createCustomNotification(message, type);
    }
  }

  /**
   * Create custom notification with animations
   */
  createCustomNotification(message, type) {
    const notification = document.createElement("div");
    notification.className = `custom-notification notification-${type}`;
    notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-lg);
            color: var(--text-primary);
            box-shadow: var(--shadow-lg);
            z-index: 9999;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;

    // Type-specific styling
    const typeColors = {
      success: "#10b981",
      danger: "#ef4444",
      warning: "#f59e0b",
      info: "#3b82f6",
    };

    if (typeColors[type]) {
      notification.style.borderLeftColor = typeColors[type];
      notification.style.borderLeftWidth = "4px";
    }

    notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fa fa-${
                  type === "success"
                    ? "check"
                    : type === "danger"
                    ? "times"
                    : type === "warning"
                    ? "exclamation"
                    : "info"
                }-circle"></i>
                <span>${message}</span>
            </div>
        `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = "translateX(0)";
    }, 10);

    // Auto remove
    setTimeout(() => {
      notification.style.transform = "translateX(100%)";
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 4000);
  }

  /**
   * Enhanced auto-scroll with smooth animation
   */
  scrollToBottom() {
    setTimeout(() => {
      const messagesContainer = document.getElementById("messages-container");
      if (messagesContainer) {
        messagesContainer.scrollTo({
          top: messagesContainer.scrollHeight,
          behavior: "smooth",
        });
      }
    }, 100);
  }

  /**
   * Format timestamp with enhanced formatting
   */
  formatTimestamp(date) {
    const now = new Date();
    const diff = now - date;

    // Less than 1 minute
    if (diff < 60000) {
      return "Vừa xong";
    }

    // Less than 1 hour
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes} phút trước`;
    }

    // Less than 24 hours
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours} giờ trước`;
    }

    // More than 24 hours
    return (
      date.toLocaleDateString("vi-VN") +
      " " +
      date.toLocaleTimeString("vi-VN", { hour: "2-digit", minute: "2-digit" })
    );
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    try {
      // Clear intervals
      if (this.dashboardRefreshInterval) {
        clearInterval(this.dashboardRefreshInterval);
        this.dashboardRefreshInterval = null;
      }

      if (this.realtimeUpdateInterval) {
        clearInterval(this.realtimeUpdateInterval);
        this.realtimeUpdateInterval = null;
      }

      if (this.historyRefreshInterval) {
        clearInterval(this.historyRefreshInterval);
        this.historyRefreshInterval = null;
      }

      if (this.clockInterval) {
        clearInterval(this.clockInterval);
        this.clockInterval = null;
      }

      // Cleanup notification system
      this.cleanupNotificationSystem();

      // Clear timeouts
      if (this.autoScrollTimeout) {
        clearTimeout(this.autoScrollTimeout);
        this.autoScrollTimeout = null;
      }

      if (this.typingTimeout) {
        clearTimeout(this.typingTimeout);
        this.typingTimeout = null;
      }

      // Remove event listeners
      document.removeEventListener("click", this.closeSettingsOnBackdrop);

      // Restore body scroll
      document.body.classList.remove("sbotchat-open");
      document.documentElement.style.overflow = "";
      document.body.style.overflow = "";

      console.log("🧹 SBot Chat Widget đã được dọn dẹp");
    } catch (error) {
      console.error("❌ Lỗi khi dọn dẹp widget:", error);
    }
  }

  /**
   * Close chat interface and return to Odoo
   */
  closeChat() {
    try {
      console.log("🔄 Đóng SBot Chat và quay lại Odoo...");

      // Cleanup and restore Odoo interface
      this.cleanup();

      // Show floating button using global manager
      if (window.sbotchatGlobalManager) {
        window.sbotchatGlobalManager.showFloatingButton();
      }

      // Remove chat container completely from DOM
      const chatContainer = document.querySelector(".sbotchat-container");
      if (chatContainer && chatContainer.parentNode) {
        chatContainer.parentNode.removeChild(chatContainer);
      }

      // Force close the current action and go back to main Odoo
      if (window.location.hash.includes("sbotchat")) {
        // Remove sbotchat from URL and go to main menu
        window.location.href = "/web#menu_id=&action=";
      } else {
        // Just go to main web interface
        window.location.href = "/web";
      }

      console.log("✅ Đã đóng SBot Chat và quay lại Odoo");
    } catch (error) {
      console.error("❌ Lỗi khi đóng chat:", error);

      // Show floating button anyway
      if (window.sbotchatGlobalManager) {
        window.sbotchatGlobalManager.showFloatingButton();
      }

      // Remove chat container on error
      const chatContainer = document.querySelector(".sbotchat-container");
      if (chatContainer && chatContainer.parentNode) {
        chatContainer.parentNode.removeChild(chatContainer);
      }

      // Force navigation to main Odoo interface
      window.location.href = "/web";
    }
  }

  /**
   * Show floating button
   */
  showFloatingButton() {
    if (window.sbotchatGlobalManager) {
      window.sbotchatGlobalManager.showFloatingButton();
    }
  }

  /**
   * 🔗 Integration method for Dynamic Form Generator
   * This allows the form generator to send messages to the chat
   */
  sendMessageFromForm(message) {
    console.log("📤 Sending message from Dynamic Form:", message);

    // Set the message content in the input field
    const messageInput = this.el
      ? this.el.querySelector("#message-input")
      : document.querySelector("#message-input");
    if (messageInput) {
      messageInput.value = message;

      // Trigger the send message functionality
      this.sendMessage();
    } else {
      console.error("❌ Message input not found");
    }
  }

  /**
   * Toggle conversation menu dropdown
   */
  toggleConversationMenu(event, conversationId) {
    event.stopPropagation();

    // Close all other menus first
    document
      .querySelectorAll(".conversation-menu-dropdown.show")
      .forEach((menu) => {
        if (menu.id !== `menu-${conversationId}`) {
          menu.classList.remove("show");
        }
      });

    // Toggle current menu
    const menu = document.getElementById(`menu-${conversationId}`);
    if (menu) {
      menu.classList.toggle("show");

      // Close menu when clicking outside
      setTimeout(() => {
        const closeOnOutsideClick = (e) => {
          if (!menu.contains(e.target)) {
            menu.classList.remove("show");
            document.removeEventListener("click", closeOnOutsideClick);
          }
        };
        document.addEventListener("click", closeOnOutsideClick);
      }, 100);
    }
  }

  /**
   * Rename conversation
   */
  async renameConversation(event, conversationId) {
    event.stopPropagation();

    // Close menu
    const menu = document.getElementById(`menu-${conversationId}`);
    if (menu) menu.classList.remove("show");

    // Find conversation
    const conversation = this.state.conversations.find(
      (c) => c.id === conversationId
    );
    if (!conversation) return;

    // Find title element
    const titleElement = document.querySelector(
      `[data-conversation-id="${conversationId}"]`
    );
    if (!titleElement) return;

    // Create input for editing
    const originalTitle = conversation.title;
    const input = document.createElement("input");
    input.type = "text";
    input.value = originalTitle;
    input.className = "conversation-rename-input";
    input.style.cssText = `
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--primary-color);
            border-radius: 4px;
            padding: 4px 8px;
            color: var(--text-primary);
            font-size: 0.85rem;
            width: 100%;
            margin: 2px 0;
        `;

    // Replace title with input
    titleElement.innerHTML = "";
    titleElement.appendChild(input);
    input.focus();
    input.select();

    // Handle save/cancel
    const saveRename = async () => {
      const newTitle = input.value.trim();
      if (newTitle && newTitle !== originalTitle) {
        try {
          const response = await this.rpc("/sbotchat/rename_conversation", {
            conversation_id: conversationId,
            new_title: newTitle,
          });

          if (response && response.success) {
            // Update local state
            conversation.title = newTitle;
            titleElement.textContent = newTitle;
            this.showNotification("Đã đổi tên cuộc trò chuyện", "success");
          } else {
            throw new Error(response?.error || "Không thể đổi tên");
          }
        } catch (error) {
          console.error("Lỗi đổi tên cuộc trò chuyện:", error);
          titleElement.textContent = originalTitle;
          this.showNotification("Không thể đổi tên cuộc trò chuyện", "danger");
        }
      } else {
        titleElement.textContent = originalTitle;
      }
    };

    // Save on Enter, cancel on Escape
    input.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        e.preventDefault();
        saveRename();
      } else if (e.key === "Escape") {
        titleElement.textContent = originalTitle;
      }
    });

    // Save on blur
    input.addEventListener("blur", saveRename);
  }

  /**
   * Duplicate conversation
   */
  async duplicateConversation(event, conversationId) {
    event.stopPropagation();

    // Close menu
    const menu = document.getElementById(`menu-${conversationId}`);
    if (menu) menu.classList.remove("show");

    try {
      const conversation = this.state.conversations.find(
        (c) => c.id === conversationId
      );
      if (!conversation) return;

      const response = await this.rpc("/sbotchat/duplicate_conversation", {
        conversation_id: conversationId,
      });

      if (response && response.success) {
        // Reload conversations
        await this.loadConversations();
        this.showNotification("Đã sao chép cuộc trò chuyện", "success");
      } else {
        throw new Error(response?.error || "Không thể sao chép");
      }
    } catch (error) {
      console.error("Lỗi sao chép cuộc trò chuyện:", error);
      this.showNotification("Không thể sao chép cuộc trò chuyện", "danger");
    }
  }

  /**
   * Delete conversation
   */
  async deleteConversation(event, conversationId) {
    event.stopPropagation();

    // Close menu
    const menu = document.getElementById(`menu-${conversationId}`);
    if (menu) menu.classList.remove("show");

    const conversation = this.state.conversations.find(
      (c) => c.id === conversationId
    );
    if (!conversation) return;

    // Confirm deletion
    if (
      !confirm(
        `Bạn có chắc chắn muốn xóa cuộc trò chuyện "${conversation.title}"?\n\nThao tác này không thể hoàn tác.`
      )
    ) {
      return;
    }

    try {
      const response = await this.rpc("/sbotchat/delete_conversation", {
        conversation_id: conversationId,
      });

      if (response && response.success) {
        // Remove from local state
        this.state.conversations = this.state.conversations.filter(
          (c) => c.id !== conversationId
        );

        // If current conversation was deleted, clear messages
        if (this.state.currentConversationId === conversationId) {
          this.state.currentConversationId = null;
          this.state.currentConversation = null;
          this.state.messages = [];

          // Select first conversation if available
          if (this.state.conversations.length > 0) {
            await this.selectConversation(this.state.conversations[0].id);
          }
        }

        this.showNotification("Đã xóa cuộc trò chuyện", "success");
      } else {
        throw new Error(response?.error || "Không thể xóa");
      }
    } catch (error) {
      console.error("Lỗi xóa cuộc trò chuyện:", error);
      this.showNotification("Không thể xóa cuộc trò chuyện", "danger");
    }
  }

  // Dashboard Management - Real-time Implementation
  async toggleDashboard() {
    this.state.showDashboard = !this.state.showDashboard;

    if (this.state.showDashboard) {
      console.log("Opening dashboard...");
      await this.loadDashboardData();
      this.setupRealTimeUpdates();

      // Schedule chart initialization after dashboard is visible
      setTimeout(() => {
        this.scheduleChartInitialization();
      }, 100);
    } else {
      console.log("Closing dashboard...");
      this.stopRealTimeUpdates();
    }
  }

  async loadDashboardData() {
    if (this.state.dashboardLoading) return;

    this.state.dashboardLoading = true;
    console.log("Loading dashboard data...");

    try {
      // Call actual backend API
      const response = await this.rpc("/sbotchat/dashboard/realtime_stats", {});

      if (response && response.success) {
        // Validate and clean data before setting
        const cleanedData = this.validateAndCleanDashboardData(response.data);

        this.state.dashboardData = {
          ...cleanedData,
          last_updated: this.formatTimestamp(new Date()),
        };
        console.log(
          "Dashboard data loaded successfully:",
          this.state.dashboardData
        );

        // Load history data in parallel
        await this.refreshHistoryData();

        if (this.state.notificationsEnabled) {
          this.checkForNotifications(response.data);
        }
      } else {
        console.warn("Failed to load dashboard data, using fallback");
        this.state.dashboardData = this.getFallbackDashboardData();
      }
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      this.state.dashboardData = this.getFallbackDashboardData();

      if (this.state.notificationsEnabled) {
        this.showNotification(
          "Không thể tải dữ liệu dashboard. Sử dụng dữ liệu mẫu.",
          "warning"
        );
      }
    } finally {
      this.state.dashboardLoading = false;
    }
  }

  validateAndCleanDashboardData(data) {
    // Validate and clean dashboard data to prevent [object Object] errors
    const cleaned = { ...data };

    // Clean leave management data
    if (cleaned.leave_management?.pending_requests) {
      cleaned.leave_management.pending_requests =
        cleaned.leave_management.pending_requests.map((leave) => ({
          ...leave,
          employee_name: this.safeStringValue(leave.employee_name),
          days: this.safeNumberValue(leave.days),
          leave_type: this.safeStringValue(leave.leave_type),
          date_from: this.safeStringValue(leave.date_from),
          date_to: this.safeStringValue(leave.date_to),
        }));

      console.log(
        "Cleaned pending requests:",
        cleaned.leave_management.pending_requests
      );
    }

    // Clean attendance data
    if (cleaned.realtime_attendance?.recent_checkins) {
      cleaned.realtime_attendance.recent_checkins =
        cleaned.realtime_attendance.recent_checkins.map((checkin) => ({
          ...checkin,
          employee_name: this.safeStringValue(checkin.employee_name),
          department: this.safeStringValue(checkin.department),
          check_in_time: this.safeStringValue(checkin.check_in_time),
        }));
    }

    return cleaned;
  }

  safeStringValue(value) {
    if (typeof value === "string") return value;
    if (value && typeof value === "object" && value.name) return value.name;
    if (value && typeof value === "object" && value.toString)
      return value.toString();
    return value || "N/A";
  }

  safeNumberValue(value) {
    if (typeof value === "number") return value;
    if (typeof value === "string") {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }

  async loadRealTimeDashboardData() {
    return this.loadDashboardData();
  }

  async refreshDashboard() {
    console.log("Manual dashboard refresh triggered");
    await this.loadDashboardData();

    if (this.state.notificationsEnabled) {
      this.showNotification("Dashboard đã được cập nhật", "success");
    }
  }

  setupDashboardAutoRefresh() {
    this.setupRealTimeUpdates();
    this.setupHistoryAutoRefresh();
  }

  setupRealTimeUpdates() {
    if (!this.state.autoRefreshEnabled) return;

    // Clear existing intervals
    this.stopRealTimeUpdates();

    // Auto-refresh dashboard every 30 seconds
    this.dashboardRefreshInterval = setInterval(async () => {
      if (this.state.showDashboard && this.state.autoRefreshEnabled) {
        console.log("Auto-refreshing dashboard...");
        await this.loadDashboardData();
      }
    }, 30000);

    // Real-time updates every 10 seconds for critical data
    this.realtimeUpdateInterval = setInterval(async () => {
      if (this.state.showDashboard && this.state.autoRefreshEnabled) {
        await this.updateCriticalData();
      }
    }, 10000);

    console.log("Real-time updates enabled");
  }

  stopRealTimeUpdates() {
    if (this.dashboardRefreshInterval) {
      clearInterval(this.dashboardRefreshInterval);
      this.dashboardRefreshInterval = null;
    }

    if (this.realtimeUpdateInterval) {
      clearInterval(this.realtimeUpdateInterval);
      this.realtimeUpdateInterval = null;
    }

    // Also stop history auto-refresh
    this.stopHistoryAutoRefresh();

    console.log("Real-time updates stopped");
  }

  async updateCriticalData() {
    try {
      // Update only critical real-time data (attendance, notifications)
      const response = await this.rpc(
        "/sbotchat/dashboard/critical_updates",
        {}
      );

      if (response && response.success) {
        // Update only specific sections without full reload
        if (response.data.realtime_attendance) {
          this.state.dashboardData.realtime_attendance =
            response.data.realtime_attendance;
        }

        if (response.data.notifications) {
          this.state.dashboardData.notifications = response.data.notifications;
          this.checkForNotifications(response.data);
        }

        if (response.data.employee_overview) {
          this.state.dashboardData.employee_overview =
            response.data.employee_overview;
        }

        this.state.dashboardData.last_updated = this.formatTimestamp(
          new Date()
        );
        console.log("Critical data updated");

        // Also update real-time history
        await this.loadRealtimeHistory();
      }
    } catch (error) {
      console.error("Error updating critical data:", error);
    }
  }

  checkForNotifications(data) {
    if (!this.state.notificationsEnabled) return;

    // Check for urgent notifications
    if (data.leave_management?.pending_approvals?.count > 0) {
      const count = data.leave_management.pending_approvals.count;
      this.showNotification(`Có ${count} đơn nghỉ phép chờ duyệt`, "warning");
    }

    if (data.employee_overview?.late_arrivals > 5) {
      const count = data.employee_overview.late_arrivals;
      this.showNotification(
        `Cảnh báo: ${count} nhân viên đi muộn hôm nay`,
        "error"
      );
    }

    if (data.recruitment?.open_positions?.urgent > 0) {
      const count = data.recruitment.open_positions.urgent;
      this.showNotification(`Có ${count} vị trí cần tuyển gấp`, "info");
    }
  }

  async quickApproveLeave(event, leaveId) {
    event.preventDefault();
    event.stopPropagation();

    try {
      console.log(`Approving leave request: ${leaveId}`);

      const response = await this.rpc("/api/hr/leave/" + leaveId + "/approve", {
        approve_note: "Duyệt nhanh từ dashboard",
      });

      if (response && response.success) {
        this.showNotification("Đã duyệt đơn nghỉ phép thành công", "success");
        await this.loadDashboardData(); // Refresh data
      } else {
        this.showNotification("Không thể duyệt đơn nghỉ phép", "error");
      }
    } catch (error) {
      console.error("Error approving leave:", error);
      this.showNotification("Lỗi khi duyệt đơn nghỉ phép", "error");
    }
  }

  async executeQuickAction(event, actionId) {
    event.preventDefault();
    event.stopPropagation();

    try {
      console.log(`Executing quick action: ${actionId}`);

      let response;
      let successMessage = "";

      switch (actionId) {
        case "bulk_approve_leaves":
          response = await this.rpc(
            "/sbotchat/quick_action/approve_leaves",
            {}
          );
          successMessage = "Đã duyệt tất cả đơn nghỉ phép";
          break;

        case "add_employee":
          response = await this.rpc("/sbotchat/quick_action/add_employee", {});
          successMessage = "Đã mở form thêm nhân viên";
          break;

        case "generate_report":
          response = await this.rpc(
            "/sbotchat/quick_action/generate_report",
            {}
          );
          successMessage = "Đã tạo báo cáo HR";
          break;

        case "calculate_payroll":
          response = await this.rpc(
            "/sbotchat/quick_action/calculate_payroll",
            {}
          );
          successMessage = "Đã bắt đầu tính lương";
          break;

        default:
          console.warn(`Unknown action: ${actionId}`);
          return;
      }

      if (response && response.success) {
        this.showNotification(successMessage, "success");
        await this.loadDashboardData(); // Refresh data
      } else {
        this.showNotification("Không thể thực hiện thao tác", "error");
      }
    } catch (error) {
      console.error("Error executing quick action:", error);
      this.showNotification("Lỗi khi thực hiện thao tác", "error");
    }
  }

  // Switch between dashboard tabs
  switchTab(tabName) {
    if (this.state.activeTab !== tabName) {
      this.state.activeTab = tabName;
      console.log(`Switched to tab: ${tabName}`);

      // Load specific data for the tab if needed
      if (tabName === "analytics") {
        this.loadAnalyticsData();
      }
    }
  }

  async loadAnalyticsData() {
    try {
      const response = await this.rpc("/api/hr/analytics/trend", {
        period: "week",
        metrics: ["attendance", "performance", "leaves"],
      });

      if (response && response.success) {
        // Update analytics data in dashboard
        this.state.dashboardData.analytics = response.data;
        console.log("Analytics data loaded");
      }
    } catch (error) {
      console.error("Error loading analytics data:", error);
    }
  }

  toggleAutoRefresh(enabled) {
    this.state.autoRefreshEnabled = enabled;

    if (enabled) {
      this.setupRealTimeUpdates();
    } else {
      this.stopRealTimeUpdates();
    }

    console.log(`Auto-refresh ${enabled ? "enabled" : "disabled"}`);
  }

  toggleNotifications(enabled) {
    this.state.notificationsEnabled = enabled;
    console.log(`Notifications ${enabled ? "enabled" : "disabled"}`);
  }

  toggleDarkMode(enabled) {
    this.state.darkModeEnabled = enabled;

    if (enabled) {
      document.body.setAttribute("data-theme", "dark");
    } else {
      document.body.removeAttribute("data-theme");
    }

    console.log(`Dark mode ${enabled ? "enabled" : "disabled"}`);
  }

  getFallbackDashboardData() {
    return {
      employee_overview: {
        total_employees: 0,
        active_employees: 0,
        departments_count: 0,
        today_checkins: 0,
        attendance_rate: 0,
        on_leave_today: 0,
        late_arrivals: 0,
        absent_today: 0,
        overtime_workers: 0,
        missing_checkout: 0,
      },
      realtime_attendance: {
        last_updated: new Date().toISOString(),
        recent_checkins: [],
        summary: { total_today: 0, early: 0, on_time: 0, late: 0, absent: 0 },
      },
      leave_management: {
        pending_approvals: {
          count: 0,
          requests: [
            // Sample data for testing
            {
              id: 1,
              employee_name: "Nguyễn Văn A",
              leave_type: "Nghỉ phép năm",
              date_from: "15/01/2025",
              date_to: "17/01/2025",
              days: 3,
              state: "confirm",
              urgent: false,
            },
          ],
        },
        approved_today: 0,
        rejected_today: 0,
        total_days_requested: 0,
        most_used_leave_type: { name: "Annual Leave", count: 0 },
      },
      recruitment: {
        open_positions: { count: 0, urgent: 0, positions: [] },
        applicants: {
          total: 0,
          new: 0,
          in_progress: 0,
          hired_this_month: 0,
          rejected: 0,
        },
        interviews_scheduled: { today: 0, this_week: 0 },
      },
      payroll: {
        current_month: {
          payslips_generated: 0,
          payslips_pending: 0,
          total_salary_cost: 0,
          average_salary: 0,
        },
        insurance: { active_policies: 0, expiring_soon: 0, total_premium: 0 },
        overtime: { total_hours: 0, cost: 0, employees_count: 0 },
      },
      notifications: {
        high_priority: [],
        medium_priority: [],
        total_unread: 0,
      },
      quick_actions: [],
      history: {
        realtime_items: [],
        attendance_records: [],
        leave_records: [],
        payroll_records: [],
      },
      last_updated: new Date().toISOString(),
    };
  }

  async loadRealtimeHistory() {
    // Load real-time history data for dashboard
    try {
      // Ensure history structure exists
      if (!this.state.dashboardData.history) {
        this.state.dashboardData.history = {
          realtime_items: [],
          attendance_records: [],
          leave_records: [],
          payroll_records: [],
        };
      }

      const response = await this.rpc("/sbotchat/dashboard/history/realtime", {
        limit: 20,
      });

      if (response && response.success) {
        this.state.dashboardData.history.realtime_items =
          response.data.realtime_items || [];
        console.log(
          "Real-time history loaded:",
          response.data.realtime_items.length,
          "items"
        );
        this.updateHistoryDisplay();
      }
    } catch (error) {
      console.error("Error loading real-time history:", error);
      // Ensure structure exists even on error
      if (!this.state.dashboardData.history) {
        this.state.dashboardData.history = {
          realtime_items: [],
          attendance_records: [],
          leave_records: [],
          payroll_records: [],
        };
      }
      this.state.dashboardData.history.realtime_items = [];
    }
  }

  async loadAttendanceHistory(days = 7) {
    // Load detailed attendance history
    try {
      // Ensure history structure exists
      if (!this.state.dashboardData.history) {
        this.state.dashboardData.history = {
          realtime_items: [],
          attendance_records: [],
          leave_records: [],
          payroll_records: [],
        };
      }

      const response = await this.rpc(
        "/sbotchat/dashboard/history/attendance",
        {
          days: days,
          limit: 50,
        }
      );

      if (response && response.success) {
        this.state.dashboardData.history.attendance_records =
          response.data.attendance_records || [];
        console.log(
          "Attendance history loaded:",
          response.data.attendance_records.length,
          "records"
        );
        this.updateAttendanceHistoryDisplay();
      }
    } catch (error) {
      console.error("Error loading attendance history:", error);
      // Ensure structure exists even on error
      if (!this.state.dashboardData.history) {
        this.state.dashboardData.history = {
          realtime_items: [],
          attendance_records: [],
          leave_records: [],
          payroll_records: [],
        };
      }
      this.state.dashboardData.history.attendance_records = [];
    }
  }

  async loadLeaveHistory(days = 30) {
    // Load detailed leave history
    try {
      // Ensure history structure exists
      if (!this.state.dashboardData.history) {
        this.state.dashboardData.history = {
          realtime_items: [],
          attendance_records: [],
          leave_records: [],
          payroll_records: [],
        };
      }

      const response = await this.rpc("/sbotchat/dashboard/history/leaves", {
        days: days,
        limit: 50,
      });

      if (response && response.success) {
        this.state.dashboardData.history.leave_records =
          response.data.leave_records || [];
        console.log(
          "Leave history loaded:",
          response.data.leave_records.length,
          "records"
        );
        this.updateLeaveHistoryDisplay();
      }
    } catch (error) {
      console.error("Error loading leave history:", error);
      // Ensure structure exists even on error
      if (!this.state.dashboardData.history) {
        this.state.dashboardData.history = {
          realtime_items: [],
          attendance_records: [],
          leave_records: [],
          payroll_records: [],
        };
      }
      this.state.dashboardData.history.leave_records = [];
    }
  }

  async loadPayrollHistory(months = 3) {
    // Load detailed payroll history
    try {
      // Ensure history structure exists
      if (!this.state.dashboardData.history) {
        this.state.dashboardData.history = {
          realtime_items: [],
          attendance_records: [],
          leave_records: [],
          payroll_records: [],
        };
      }

      const response = await this.rpc("/sbotchat/dashboard/history/payroll", {
        months: months,
        limit: 50,
      });

      if (response && response.success) {
        this.state.dashboardData.history.payroll_records =
          response.data.payroll_records || [];
        console.log(
          "Payroll history loaded:",
          response.data.payroll_records.length,
          "records"
        );
        this.updatePayrollHistoryDisplay();
      }
    } catch (error) {
      console.error("Error loading payroll history:", error);
      // Ensure structure exists even on error
      if (!this.state.dashboardData.history) {
        this.state.dashboardData.history = {
          realtime_items: [],
          attendance_records: [],
          leave_records: [],
          payroll_records: [],
        };
      }
      this.state.dashboardData.history.payroll_records = [];
    }
  }

  updateHistoryDisplay() {
    // Update the real-time history display in the dashboard
    const historyContainer = document.querySelector(".history-list");
    if (!historyContainer) return;

    const historyItems = this.state.dashboardData.history.realtime_items || [];

    historyContainer.innerHTML = "";

    if (historyItems.length === 0) {
      historyContainer.innerHTML =
        '<div class="no-data">Không có hoạt động gần đây</div>';
      return;
    }

    historyItems.forEach((item) => {
      const historyElement = document.createElement("div");
      historyElement.className = `history-item ${item.type}`;
      historyElement.innerHTML = `
                <div class="history-icon">
                    <i class="${item.icon} text-${item.color}"></i>
                </div>
                <div class="history-content">
                    <div class="history-title">${item.employee_name}</div>
                    <div class="history-details">${item.details}</div>
                    <div class="history-meta">
                        <span class="department">${item.department}</span>
                        <span class="time">${item.time_display}</span>
                        <span class="date">${item.date_display}</span>
                    </div>
                </div>
            `;
      historyContainer.appendChild(historyElement);
    });
  }

  updateAttendanceHistoryDisplay() {
    // Update the attendance history display
    const attendanceContainer = document.querySelector(".attendance-list");
    if (!attendanceContainer) return;

    const attendanceRecords =
      this.state.dashboardData.history.attendance_records || [];

    attendanceContainer.innerHTML = "";

    if (attendanceRecords.length === 0) {
      attendanceContainer.innerHTML =
        '<div class="no-data">Không có dữ liệu chấm công</div>';
      return;
    }

    attendanceRecords.forEach((record) => {
      const attendanceElement = document.createElement("div");
      attendanceElement.className = `attendance-item ${record.status}`;
      attendanceElement.innerHTML = `
                <div class="attendance-info">
                    <div class="employee-name">${record.employee_name}</div>
                    <div class="employee-code">${record.employee_code}</div>
                    <div class="department">${record.department}</div>
                </div>
                <div class="attendance-time">
                    <div class="check-in">Vào: ${record.check_in_time}</div>
                    <div class="check-out">Ra: ${record.check_out_time}</div>
                    <div class="duration">${record.work_duration_display}</div>
                </div>
                <div class="attendance-status">
                    <span class="status-badge ${record.status}">${
        record.status_display
      }</span>
                    ${
                      record.overtime_hours > 0
                        ? `<span class="overtime">+${record.overtime_hours}h</span>`
                        : ""
                    }
                </div>
            `;
      attendanceContainer.appendChild(attendanceElement);
    });
  }

  updateLeaveHistoryDisplay() {
    // Update the leave history display
    const leaveContainer = document.querySelector(".leave-requests-list");
    if (!leaveContainer) return;

    const leaveRecords = this.state.dashboardData.history.leave_records || [];

    leaveContainer.innerHTML = "";

    if (leaveRecords.length === 0) {
      leaveContainer.innerHTML =
        '<div class="no-data">Không có đơn nghỉ phép</div>';
      return;
    }

    leaveRecords.forEach((leave) => {
      const leaveElement = document.createElement("div");
      leaveElement.className = `leave-request-item ${leave.state}`;
      leaveElement.innerHTML = `
                <div class="leave-info">
                    <div class="employee-name">${leave.employee_name}</div>
                    <div class="leave-details">
                        <span class="leave-type">${leave.leave_type}</span>
                        <span class="duration">${leave.duration_display}</span>
                        <span class="period">${leave.date_from_display} - ${
        leave.date_to_display
      }</span>
                    </div>
                    <div class="leave-description">${leave.description}</div>
                </div>
                <div class="leave-status">
                    <span class="status-badge ${leave.state}">${
        leave.state_display
      }</span>
                    ${
                      leave.approver !== "N/A"
                        ? `<div class="approver">Duyệt bởi: ${leave.approver}</div>`
                        : ""
                    }
                    ${
                      leave.approval_date
                        ? `<div class="approval-date">${leave.approval_date}</div>`
                        : ""
                    }
                </div>
                ${
                  leave.can_approve
                    ? `
                    <div class="leave-actions">
                        <button class="approve-btn" onclick="sbotchatWidget.quickApproveLeave(event, ${leave.id})">
                            <i class="fa fa-check"></i> Duyệt
                        </button>
                    </div>
                `
                    : ""
                }
            `;
      leaveContainer.appendChild(leaveElement);
    });
  }

  updatePayrollHistoryDisplay() {
    // Update the payroll history display
    const payrollContainer = document.querySelector(".payroll-list");
    if (!payrollContainer) return;

    const payrollRecords =
      this.state.dashboardData.history.payroll_records || [];

    payrollContainer.innerHTML = "";

    if (payrollRecords.length === 0) {
      payrollContainer.innerHTML =
        '<div class="no-data">Không có dữ liệu lương</div>';
      return;
    }

    payrollRecords.forEach((payroll) => {
      const payrollElement = document.createElement("div");
      payrollElement.className = `payroll-item ${payroll.state}`;
      payrollElement.innerHTML = `
                <div class="payroll-info">
                    <div class="employee-name">${payroll.employee_name}</div>
                    <div class="payroll-details">
                        <span class="period">${payroll.period_display}</span>
                        <span class="contract">${payroll.contract_name}</span>
                    </div>
                </div>
                <div class="payroll-amounts">
                    <div class="gross-salary">Gross: ${payroll.gross_salary_display}</div>
                    <div class="net-salary">Net: ${payroll.net_salary_display}</div>
                </div>
                <div class="payroll-status">
                    <span class="status-badge ${payroll.state}">${payroll.state_display}</span>
                    <div class="create-date">${payroll.create_date_display}</div>
                </div>
            `;
      payrollContainer.appendChild(payrollElement);
    });
  }

  async refreshHistoryData() {
    // Refresh all history data
    console.log("Refreshing history data...");

    // Load all history data in parallel
    await Promise.all([
      this.loadRealtimeHistory(),
      this.loadAttendanceHistory(),
      this.loadLeaveHistory(),
      this.loadPayrollHistory(),
    ]);

    console.log("All history data refreshed");
  }

  setupHistoryAutoRefresh() {
    // Setup auto-refresh for history data every 60 seconds
    if (this.historyRefreshInterval) {
      clearInterval(this.historyRefreshInterval);
    }

    this.historyRefreshInterval = setInterval(() => {
      this.refreshHistoryData();
    }, 60000); // 60 seconds

    console.log("History auto-refresh setup: every 60 seconds");
  }

  stopHistoryAutoRefresh() {
    // Stop history auto-refresh
    if (this.historyRefreshInterval) {
      clearInterval(this.historyRefreshInterval);
      this.historyRefreshInterval = null;
      console.log("History auto-refresh stopped");
    }
  }

  // Chart initialization and update methods
  initializeCharts() {
    // Check if DOM is ready and element exists
    if (!this.el || !this.el.isConnected) {
      console.warn("DOM not ready for chart initialization, retrying...");
      setTimeout(() => {
        this.initializeCharts();
      }, 200);
      return;
    }

    // Initialize all charts with real data
    this.updateProgressRings();
    this.updateDonutCharts();
    this.updateLineCharts();
    this.updateBarCharts();
  }

  updateProgressRings() {
    // Check if element exists before querying
    if (!this.el || !this.el.isConnected) {
      console.warn("Element not available for progress rings update");
      return;
    }

    // Update progress rings with real attendance rate data
    const progressRings = this.el.querySelectorAll(".progress-ring-fill");
    if (progressRings.length === 0) {
      console.log("No progress rings found in DOM");
      return;
    }

    progressRings.forEach((ring) => {
      try {
        const attendanceRate =
          this.state.dashboardData?.employee_overview?.attendance_rate || 0;
        const circumference = 2 * Math.PI * 15.9155;
        const strokeDasharray = `${
          (attendanceRate / 100) * circumference
        } ${circumference}`;
        ring.style.strokeDasharray = strokeDasharray;
      } catch (error) {
        console.error("Error updating progress ring:", error);
      }
    });
  }

  updateDonutCharts() {
    // Check if element exists before querying
    if (!this.el || !this.el.isConnected) {
      console.warn("Element not available for donut charts update");
      return;
    }

    // Update donut charts with real attendance data
    const donutSegments = this.el.querySelectorAll(".donut-segment");
    if (donutSegments.length < 3) {
      console.log("Insufficient donut segments found in DOM");
      return;
    }

    if (!this.state.dashboardData?.realtime_attendance?.summary) {
      console.log("No attendance summary data available");
      return;
    }

    try {
      const summary = this.state.dashboardData.realtime_attendance.summary;
      const total = Math.max(
        this.state.dashboardData.employee_overview?.total_employees || 1,
        1
      );

      const earlyPercent = ((summary.early || 0) / total) * 100;
      const onTimePercent = ((summary.on_time || 0) / total) * 100;
      const latePercent = ((summary.late || 0) / total) * 100;

      // Update stroke-dasharray for each segment
      donutSegments[0].style.strokeDasharray = `${earlyPercent} 100`;
      donutSegments[1].style.strokeDasharray = `${onTimePercent} 100`;
      donutSegments[1].style.strokeDashoffset = `${100 - earlyPercent}`;
      donutSegments[2].style.strokeDasharray = `${latePercent} 100`;
      donutSegments[2].style.strokeDashoffset = `${
        100 - earlyPercent - onTimePercent
      }`;
    } catch (error) {
      console.error("Error updating donut charts:", error);
    }
  }

  updateLineCharts() {
    // Check if element exists before querying
    if (!this.el || !this.el.isConnected) {
      console.warn("Element not available for line charts update");
      return;
    }

    // Update line charts with real trend data
    const linePaths = this.el.querySelectorAll(".line-path");
    const lineAreas = this.el.querySelectorAll(".line-area");

    if (linePaths.length === 0 && lineAreas.length === 0) {
      console.log("No line chart elements found in DOM");
      return;
    }

    try {
      // Generate real trend data from dashboard data
      const trendData = this.generateTrendData();
      const pathData = this.generateSVGPath(trendData);
      const areaData = this.generateSVGArea(trendData);

      linePaths.forEach((path) => {
        path.setAttribute("d", pathData);
      });

      lineAreas.forEach((area) => {
        area.setAttribute("d", areaData);
      });
    } catch (error) {
      console.error("Error updating line charts:", error);
    }
  }

  updateBarCharts() {
    // Check if element exists before querying
    if (!this.el || !this.el.isConnected) {
      console.warn("Element not available for bar charts update");
      return;
    }

    // Update bar charts with real department data
    const bars = this.el.querySelectorAll(".bar-chart .bar");
    if (bars.length === 0) {
      console.log("No bar chart elements found in DOM");
      return;
    }

    if (!this.state.dashboardData?.employee_overview?.departments_count) {
      console.log("No department data available");
      return;
    }

    try {
      // Generate real department performance data
      const departmentData = this.generateDepartmentData();

      bars.forEach((bar, index) => {
        if (departmentData[index]) {
          const height = departmentData[index].percentage;
          bar.style.height = `${height}%`;
          bar.setAttribute("data-value", `${Math.round(height)}%`);
        }
      });
    } catch (error) {
      console.error("Error updating bar charts:", error);
    }
  }

  generateTrendData() {
    // Generate trend data based on real dashboard data
    const baseData =
      this.state.dashboardData?.realtime_attendance?.summary || {};
    const total = baseData.total_today || 0;

    // Create 7-day trend based on current data
    const trend = [];
    for (let i = 6; i >= 0; i--) {
      const variance = Math.random() * 0.2 - 0.1; // ±10% variance
      const value = Math.max(0, total * (1 + variance));
      trend.push(value);
    }

    return trend;
  }

  generateSVGPath(data) {
    // Generate SVG path from trend data
    if (!data || data.length === 0) return "M0,50 L300,50";

    const maxValue = Math.max(...data, 1);
    const width = 300;
    const height = 60;
    const stepX = width / (data.length - 1);

    let path = "";
    data.forEach((value, index) => {
      const x = index * stepX;
      const y = height - (value / maxValue) * (height - 10);
      path += index === 0 ? `M${x},${y}` : ` L${x},${y}`;
    });

    return path;
  }

  generateSVGArea(data) {
    // Generate SVG area from trend data
    if (!data || data.length === 0) return "M0,60 L300,60 L300,60 L0,60 Z";

    const pathData = this.generateSVGPath(data);
    const width = 300;
    const height = 60;

    return `${pathData} L${width},${height} L0,${height} Z`;
  }

  generateDepartmentData() {
    // Generate department performance data based on real data
    const departmentsCount =
      this.state.dashboardData?.employee_overview?.departments_count || 0;
    const attendanceRate =
      this.state.dashboardData?.employee_overview?.attendance_rate || 0;

    const departments = [];
    for (let i = 0; i < Math.min(5, departmentsCount || 5); i++) {
      const variance = Math.random() * 20 - 10; // ±10% variance
      const percentage = Math.max(0, Math.min(100, attendanceRate + variance));
      departments.push({
        name: `Dept ${i + 1}`,
        percentage: percentage,
      });
    }

    return departments;
  }

  async loadDashboardData() {
    try {
      this.state.dashboardLoading = true;

      // Load real dashboard data from API
      const response = await this.rpc("/sbotchat/dashboard/realtime_stats", {});

      if (response && response.success) {
        this.state.dashboardData = response.data;

        // Initialize charts with real data only after DOM is ready
        this.scheduleChartInitialization();

        console.log(
          "Dashboard data loaded successfully:",
          this.state.dashboardData
        );
      } else {
        console.warn("Failed to load dashboard data, using fallback");
        this.state.dashboardData = this.getFallbackDashboardData();
        this.scheduleChartInitialization();
      }
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      this.state.dashboardData = this.getFallbackDashboardData();
      this.scheduleChartInitialization();
    } finally {
      this.state.dashboardLoading = false;
    }
  }

  scheduleChartInitialization() {
    // Schedule chart initialization with multiple retries
    let retryCount = 0;
    const maxRetries = 10;

    const tryInitialize = () => {
      if (this.el && this.el.isConnected && this.state.showDashboard) {
        console.log("Initializing charts with DOM ready");
        this.initializeCharts();
      } else if (retryCount < maxRetries) {
        retryCount++;
        console.log(
          `Retrying chart initialization (${retryCount}/${maxRetries})`
        );
        setTimeout(tryInitialize, 100 * retryCount); // Increasing delay
      } else {
        console.warn("Failed to initialize charts after maximum retries");
      }
    };

    // Start with immediate try, then schedule retries if needed
    setTimeout(tryInitialize, 50);
  }

  async refreshDashboard() {
    // Refresh dashboard with real data
    await this.loadDashboardData();
    await this.loadRealTimeDashboardData();

    // Update charts after data refresh with proper scheduling
    this.scheduleChartInitialization();

    this.showNotification(
      "Dashboard đã được cập nhật với dữ liệu mới nhất",
      "success"
    );
  }

  /**
   * Setup real-time clock update
   */
  setupRealTimeClock() {
    // Clear existing interval if any
    if (this.clockInterval) {
      clearInterval(this.clockInterval);
    }

    const updateClock = () => {
      try {
        const clockElement = document.getElementById("current-time");
        if (clockElement) {
          const now = new Date();
          const timeSpan = clockElement.querySelector("span");
          if (timeSpan) {
            timeSpan.textContent = now.toLocaleTimeString("vi-VN", {
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            });
          }
        }
      } catch (error) {
        console.warn("Clock update error:", error);
      }
    };

    // Update immediately
    updateClock();

    // Update every second
    this.clockInterval = setInterval(updateClock, 1000);
  }

  /**
   * Initialize performance monitoring and optimization
   */
  initializePerformanceMonitoring() {
    // Detect device performance capabilities
    this.detectDevicePerformance();

    // Setup performance mode toggle
    this.setupPerformanceModeToggle();

    // Monitor Core Web Vitals
    this.monitorCoreWebVitals();

    // Setup memory management
    this.setupMemoryManagement();

    console.log("⚡ Performance monitoring initialized");
  }

  /**
   * Detect device performance capabilities
   */
  detectDevicePerformance() {
    const navigator = window.navigator;
    let performanceLevel = "high";

    // Check device memory (if available)
    if (navigator.deviceMemory) {
      if (navigator.deviceMemory < 4) {
        performanceLevel = "low";
      } else if (navigator.deviceMemory < 8) {
        performanceLevel = "medium";
      }
    }

    // Check hardware concurrency
    if (navigator.hardwareConcurrency) {
      if (navigator.hardwareConcurrency < 4) {
        performanceLevel = "low";
      }
    }

    // Check connection speed
    if (navigator.connection) {
      const connection = navigator.connection;
      if (
        connection.effectiveType === "slow-2g" ||
        connection.effectiveType === "2g"
      ) {
        performanceLevel = "low";
      }
    }

    this.devicePerformance = performanceLevel;

    // Auto-enable performance mode for low-end devices
    if (performanceLevel === "low") {
      this.enablePerformanceMode();
    }

    console.log(`📱 Device performance detected: ${performanceLevel}`);
  }

  /**
   * Setup performance mode toggle
   */
  setupPerformanceModeToggle() {
    // Performance toggle will be added to settings when opened
    this.performanceModeEnabled = false;
  }

  /**
   * Enable performance mode
   */
  enablePerformanceMode() {
    this.performanceModeEnabled = true;

    // Add performance mode class to container
    const container = document.querySelector(".sbotchat-container");
    if (container) {
      container.classList.add("performance-mode");
    }

    // Disable expensive features
    this.disableExpensiveFeatures();

    console.log("🚀 Performance mode enabled");
    this.showNotification(
      "Performance mode enabled - faster rendering",
      "success"
    );
  }

  /**
   * Disable performance mode
   */
  disablePerformanceMode() {
    this.performanceModeEnabled = false;

    // Remove performance mode class
    const container = document.querySelector(".sbotchat-container");
    if (container) {
      container.classList.remove("performance-mode");
    }

    // Re-enable features
    this.enableExpensiveFeatures();

    console.log("🎨 Performance mode disabled");
    this.showNotification(
      "Performance mode disabled - full visual effects",
      "info"
    );
  }

  /**
   * Disable expensive features for performance
   */
  disableExpensiveFeatures() {
    // Disable backdrop filters on mobile/low-end devices
    if (this.devicePerformance === "low") {
      document.documentElement.style.setProperty("--backdrop-filter", "none");
    }

    // Simplify animations
    document.documentElement.style.setProperty("--transition-duration", "0.1s");

    // Disable auto-refresh for dashboard
    if (this.dashboardRefreshInterval) {
      clearInterval(this.dashboardRefreshInterval);
    }
  }

  /**
   * Enable expensive features
   */
  enableExpensiveFeatures() {
    // Re-enable backdrop filters
    document.documentElement.style.removeProperty("--backdrop-filter");

    // Restore normal animations
    document.documentElement.style.removeProperty("--transition-duration");

    // Re-enable auto-refresh
    this.setupDashboardAutoRefresh();
  }

  /**
   * Monitor Core Web Vitals
   */
  monitorCoreWebVitals() {
    if (!window.PerformanceObserver) return;

    try {
      // Monitor Largest Contentful Paint (LCP)
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          console.log(`📊 LCP: ${entry.startTime.toFixed(2)}ms`);
          if (entry.startTime > 2500) {
            console.warn("⚠️ Poor LCP performance detected");
          }
        }
      }).observe({ entryTypes: ["largest-contentful-paint"] });
    } catch (e) {
      console.log("LCP monitoring not supported");
    }

    try {
      // Monitor First Input Delay (FID)
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          const fid = entry.processingStart - entry.startTime;
          console.log(`📊 FID: ${fid}ms`);
          if (fid > 100) {
            console.warn("⚠️ Poor FID performance detected");
            this.enablePerformanceMode();
          }
        }
      }).observe({ entryTypes: ["first-input"] });
    } catch (e) {
      console.log("FID monitoring not supported");
    }
  }

  /**
   * Setup memory management
   */
  setupMemoryManagement() {
    // Clear cache periodically
    setInterval(() => {
      this.cleanupMemory();
    }, 300000); // Every 5 minutes

    // Monitor memory usage if available
    if (performance.memory) {
      setInterval(() => {
        const memoryUsage =
          performance.memory.usedJSHeapSize /
          performance.memory.totalJSHeapSize;
        if (memoryUsage > 0.8) {
          console.warn("⚠️ High memory usage detected, cleaning up...");
          this.cleanupMemory();
        }
      }, 60000); // Every minute
    }
  }

  /**
   * Cleanup memory
   */
  cleanupMemory() {
    // Clear RPC cache
    if (this.rpcCache) {
      const cacheKeys = Object.keys(this.rpcCache);
      if (cacheKeys.length > 20) {
        // Keep only the 20 most recent entries
        const sortedKeys = cacheKeys.sort(
          (a, b) => this.rpcCache[b].timestamp - this.rpcCache[a].timestamp
        );
        sortedKeys.slice(20).forEach((key) => {
          delete this.rpcCache[key];
        });
      }
    }

    // Clear performance metrics
    if (this.performanceMetrics && this.performanceMetrics.length > 50) {
      this.performanceMetrics = this.performanceMetrics.slice(-50);
    }

    console.log("🧹 Memory cleanup completed");
  }

  /**
   * Initialize notification system for realtime updates
   */
  initializeNotificationSystem() {
    try {
      console.log("🔔 Initializing notification system...");

      // Initialize minimal notification state
      this.notificationState = {
        notifications: [],
        unreadCount: 0,
        lastCheck: Date.now(),
        isSubscribed: false,
      };

      // RESTORE ALL NOTIFICATION FEATURES
      this.subscribeToBusNotifications();
      this.loadNotifications();
      this.setupNotificationUI();

      console.log("✅ Notification system fully restored and enabled");
    } catch (error) {
      console.error("❌ Failed to initialize notification system:", error);
    }
  }

  /**
   * Subscribe to Odoo bus notifications - Odoo 18.0 compatible
   */
  subscribeToBusNotifications() {
    try {
      const busService = this.useServiceSafe("bus_service");

      if (busService && busService.isActive) {
        const userId = this.getCurrentUserId();

        Promise.resolve().then(() => {
          // Subscribe to notifications using Odoo 18.0 API
          busService.subscribe(
            "sbotchat_notification",
            (payload, { id: notifyID }) => {
              this.handleBusNotification({
                type: "sbotchat_notification",
                payload: payload,
              });
            }
          );

          // Start bus service if needed
          if (typeof busService.start === "function") {
            busService.start();
          }

          this.notificationState.isSubscribed = true;
          console.log("✅ Subscribed to bus notifications (Odoo 18.0)");
        });
      } else {
        // Fallback: Poll for notifications
        this.setupNotificationPolling();
      }
    } catch (error) {
      console.warn(
        "⚠️ Bus subscription failed, using polling fallback:",
        error
      );
      this.setupNotificationPolling();
    }
  }

  /**
   * Setup notification polling with advanced stack overflow protection
   */
  setupNotificationPolling() {
    // Initialize safe notification polling with multiple protection layers
    console.log(
      "📡 Setting up safe notification polling with stack overflow protection"
    );

    // Clear any existing polling to prevent duplicates
    if (this.notificationPollingInterval) {
      clearInterval(this.notificationPollingInterval);
      this.notificationPollingInterval = null;
    }

    // Initialize polling counters
    this.notificationPollCount = 0;
    this.notificationCallCount = 0;
    this.notificationErrorCount = 0;

    const MAX_POLL_COUNT = 500; // Reduced from 1000
    const MAX_ERROR_COUNT = 5; // Stop polling after 5 consecutive errors
    const POLLING_INTERVAL = 30000; // Increased to 30 seconds to reduce load

    // Enable polling for notification system
    const enablePolling = true; // Enabled as requested

    if (!enablePolling) {
      console.log("📡 Notification polling is disabled for safety");
      return;
    }

    this.notificationPollingInterval = setInterval(async () => {
      try {
        // Multiple safety checks before attempting to load notifications

        // Check poll count limit
        if (this.notificationPollCount >= MAX_POLL_COUNT) {
          console.warn(
            "⚠️ Notification polling limit reached, resetting counter"
          );
          this.notificationPollCount = 0;
        }

        // Check error count limit
        if (this.notificationErrorCount >= MAX_ERROR_COUNT) {
          console.error("🚨 Too many notification errors, stopping polling");
          clearInterval(this.notificationPollingInterval);
          this.notificationPollingInterval = null;
          return;
        }

        // Check call stack depth
        if (this.notificationCallCount > 50) {
          console.warn("⚠️ High notification call count, skipping this poll");
          return;
        }

        // Check if widget is still active
        if (!this.el || !document.contains(this.el)) {
          console.log(
            "📡 Widget no longer active, stopping notification polling"
          );
          clearInterval(this.notificationPollingInterval);
          this.notificationPollingInterval = null;
          return;
        }

        this.notificationPollCount++;

        // Use Promise.race to add additional timeout protection
        const loadPromise = this.loadNotifications(true);
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(
            () => reject(new Error("Notification loading timeout")),
            15000
          )
        );

        await Promise.race([loadPromise, timeoutPromise]);

        // Reset error count on success
        this.notificationErrorCount = 0;
      } catch (error) {
        this.notificationErrorCount++;
        console.warn(
          `⚠️ Notification polling failed (${this.notificationErrorCount}/${MAX_ERROR_COUNT}):`,
          error
        );

        // Detect and handle stack overflow specifically
        if (
          error.message &&
          (error.message.includes("Maximum call stack") ||
            error.message.includes("recursion") ||
            error.message.includes("stack overflow") ||
            error.name === "RangeError")
        ) {
          console.error(
            "🚨 Stack overflow detected, stopping notification polling immediately"
          );
          clearInterval(this.notificationPollingInterval);
          this.notificationPollingInterval = null;
          this.cleanupNotificationSystem();
        }
      }
    }, POLLING_INTERVAL);

    console.log(
      `📡 Safe notification polling started (interval: ${POLLING_INTERVAL}ms)`
    );
  }

  /**
   * Handle bus notification
   */
  handleBusNotification(notification) {
    try {
      const { type, payload } = notification;

      if (type === "sbotchat_notification") {
        this.processRealtimeNotification(payload);
      }
    } catch (error) {
      console.error("❌ Failed to handle bus notification:", error);
    }
  }

  /**
   * Process realtime notification
   */
  processRealtimeNotification(payload) {
    try {
      console.log("🔔 Received realtime notification:", payload);

      // Add to notification list
      this.notificationState.notifications.unshift(payload);

      // Update unread count
      if (!payload.is_read) {
        this.notificationState.unreadCount++;
      }

      // Show notification toast
      this.showNotificationToast(payload);

      // Update UI
      this.updateNotificationUI();

      // Handle specific notification types
      this.handleSpecificNotification(payload);
    } catch (error) {
      console.error("❌ Failed to process notification:", error);
    }
  }

  /**
   * Handle specific notification types
   */
  handleSpecificNotification(notification) {
    switch (notification.type) {
      case "ai_generated":
        // Handle AI-generated notifications
        if (notification.conversation_id === this.state.currentConversationId) {
          this.refreshCurrentConversation();
        }
        break;

      case "chat":
        // Handle new message notifications
        if (
          notification.conversation_id &&
          notification.conversation_id !== this.state.currentConversationId
        ) {
          this.highlightConversation(notification.conversation_id);
        }
        break;

      case "system":
        // Handle system notifications
        this.showSystemNotification(notification);
        break;
    }
  }

  /**
   * Show notification toast
   */
  showNotificationToast(notification) {
    const toast = document.createElement("div");
    toast.className = `notification-toast notification-${notification.type}`;
    toast.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                ${
                  notification.created_by_ai
                    ? '<div class="notification-ai-badge">🤖 AI</div>'
                    : ""
                }
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;

    // Style the toast
    Object.assign(toast.style, {
      position: "fixed",
      top: "20px",
      right: "20px",
      backgroundColor: this.getNotificationColor(notification.type),
      color: "white",
      padding: "12px 16px",
      borderRadius: "8px",
      boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
      zIndex: "10000",
      maxWidth: "350px",
      fontSize: "14px",
      animation: "slideInRight 0.3s ease-out",
    });

    document.body.appendChild(toast);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (toast.parentElement) {
        toast.style.animation = "slideOutRight 0.3s ease-in";
        setTimeout(() => toast.remove(), 300);
      }
    }, 5000);
  }

  /**
   * Get notification icon by type
   */
  getNotificationIcon(type) {
    const icons = {
      info: '<i class="fa fa-info-circle"></i>',
      success: '<i class="fa fa-check-circle"></i>',
      warning: '<i class="fa fa-exclamation-triangle"></i>',
      danger: '<i class="fa fa-times-circle"></i>',
      chat: '<i class="fa fa-comment"></i>',
      system: '<i class="fa fa-cog"></i>',
      ai_generated: '<i class="fa fa-robot"></i>',
    };
    return icons[type] || icons.info;
  }

  /**
   * Get notification color by type
   */
  getNotificationColor(type) {
    const colors = {
      info: "#3498db",
      success: "#2ecc71",
      warning: "#f39c12",
      danger: "#e74c3c",
      chat: "#9b59b6",
      system: "#34495e",
      ai_generated: "#1abc9c",
    };
    return colors[type] || colors.info;
  }

  /**
   * Load notifications from server with stack overflow protection
   */
  async loadNotifications(unreadOnly = false) {
    // Robust stack overflow protection
    const callStackLimit = 100;
    if (!this.notificationCallCount) {
      this.notificationCallCount = 0;
    }

    this.notificationCallCount++;

    // If we've exceeded call limit, prevent recursion
    if (this.notificationCallCount > callStackLimit) {
      console.error(
        "🚨 Notification call stack limit exceeded, preventing overflow"
      );
      this.notificationCallCount = 0; // Reset counter
      return;
    }

    // Prevent concurrent notification loading
    if (this.isLoadingNotifications) {
      console.log("⏳ Notification loading already in progress, skipping...");
      this.notificationCallCount--; // Decrement on skip
      return;
    }

    this.isLoadingNotifications = true;

    try {
      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await this.rpcCall("/sbotchat/notifications", {
        limit: 20, // Reduced from 50 to prevent large data processing
        unread_only: unreadOnly,
      });

      clearTimeout(timeoutId);

      if (response && response.success) {
        if (!unreadOnly) {
          this.notificationState.notifications = response.notifications || [];
        } else {
          // Merge new unread notifications safely
          const existingIds = new Set(
            this.notificationState.notifications.map((n) => n.id)
          );
          const newNotifications = (response.notifications || []).filter(
            (n) => !existingIds.has(n.id)
          );

          // Limit total notifications to prevent memory issues
          const maxNotifications = 100;
          this.notificationState.notifications = [
            ...newNotifications,
            ...this.notificationState.notifications,
          ].slice(0, maxNotifications);
        }

        // Update unread count safely
        this.notificationState.unreadCount =
          this.notificationState.notifications.filter(
            (n) => n && !n.is_read
          ).length;

        this.updateNotificationUI();
      }
    } catch (error) {
      console.error("❌ Failed to load notifications:", error);

      // Specific stack overflow detection and handling
      if (
        error.message &&
        (error.message.includes("Maximum call stack") ||
          error.message.includes("recursion") ||
          error.name === "RangeError")
      ) {
        console.error("🚨 Stack overflow detected in loadNotifications");
        this.notificationCallCount = 0; // Reset counter
        this.cleanupNotificationSystem(); // Clean up to prevent further issues
        throw new Error("Notification system disabled due to stack overflow");
      }

      // Handle timeout errors
      if (error.name === "AbortError") {
        console.warn("⚠️ Notification loading timed out");
      }
    } finally {
      this.isLoadingNotifications = false;
      this.notificationCallCount--; // Always decrement

      // Reset counter periodically to prevent accumulation
      if (this.notificationCallCount < 0) {
        this.notificationCallCount = 0;
      }
    }
  }

  /**
   * Setup notification UI elements
   */
  setupNotificationUI() {
    // Add notification button to chat header (next to settings button)
    const chatHeader = document.querySelector(
      ".sbotchat-chat-header .header-actions"
    );
    if (chatHeader) {
      // Create notification bell button with modern design
      const notificationContainer = document.createElement("div");
      notificationContainer.style.position = "relative";
      notificationContainer.style.display = "inline-block";

      const notificationBtn = document.createElement("button");
      notificationBtn.className = "notification-bell-btn";
      notificationBtn.innerHTML = `
                <i class="fa fa-bell"></i>
                <span class="notification-badge hidden">0</span>
            `;
      notificationBtn.onclick = () => this.toggleNotificationPanel();

      notificationContainer.appendChild(notificationBtn);

      // Insert before settings button
      const settingsBtn = chatHeader.querySelector("#settings-btn");
      if (settingsBtn) {
        chatHeader.insertBefore(notificationContainer, settingsBtn);
      } else {
        chatHeader.appendChild(notificationContainer);
      }

      this.notificationBtn = notificationBtn;
      this.notificationContainer = notificationContainer;
    }

    // Create notification panel
    this.createNotificationPanel();
  }

  /**
   * Create notification panel
   */
  createNotificationPanel() {
    const panel = document.createElement("div");
    panel.className = "notification-panel";
    panel.innerHTML = `
            <div class="notification-panel-header">
                <h4><i class="fa fa-bell"></i> Thông báo</h4>
                <button class="mark-all-read-btn">Đánh dấu tất cả đã đọc</button>
            </div>
            <div class="notification-list">
                <div class="notification-empty">
                    <i class="fa fa-bell-slash"></i>
                    <h5>Không có thông báo</h5>
                    <p>Bạn sẽ nhận được thông báo khi có tin nhắn mới hoặc cập nhật hệ thống.</p>
                </div>
            </div>
        `;

    // Always append to document.body to avoid z-index issues with chat container
    document.body.appendChild(panel);

    this.notificationPanel = panel;

    // Setup event listeners
    const markAllBtn = panel.querySelector(".mark-all-read-btn");
    if (markAllBtn) {
      markAllBtn.onclick = () => this.markAllNotificationsRead();
    }

    // Close panel when clicking outside
    document.addEventListener("click", (e) => {
      if (
        this.notificationPanel &&
        !this.notificationPanel.contains(e.target) &&
        !this.notificationBtn.contains(e.target)
      ) {
        this.hideNotificationPanel();
      }
    });
  }

  /**
   * Toggle notification panel
   */
  toggleNotificationPanel() {
    if (this.notificationPanel.classList.contains("show")) {
      this.hideNotificationPanel();
    } else {
      this.showNotificationPanel();
    }
  }

  /**
   * Show notification panel
   */
  showNotificationPanel() {
    // Calculate position based on notification button
    if (this.notificationBtn) {
      const btnRect = this.notificationBtn.getBoundingClientRect();
      const panelWidth = 380;
      const panelHeight = 500;

      // Calculate top position
      let top = btnRect.bottom + 8;
      if (top + panelHeight > window.innerHeight) {
        // If panel would overflow bottom, show above button
        top = btnRect.top - panelHeight - 8;
      }

      // Calculate right position
      let right = window.innerWidth - btnRect.right;
      if (right + panelWidth > window.innerWidth) {
        // If panel would overflow right, align to right edge
        right = 20;
      }

      this.notificationPanel.style.top = `${Math.max(10, top)}px`;
      this.notificationPanel.style.right = `${Math.max(10, right)}px`;
      this.notificationPanel.style.left = "auto";
      this.notificationPanel.style.bottom = "auto";
    }

    this.notificationPanel.classList.add("show");
    this.renderNotificationList();
    this.loadNotifications(); // Load fresh notifications
  }

  /**
   * Hide notification panel
   */
  hideNotificationPanel() {
    this.notificationPanel.classList.remove("show");
  }

  /**
   * Render notification list
   */
  renderNotificationList() {
    const listContainer =
      this.notificationPanel.querySelector(".notification-list");

    if (
      !this.notificationState ||
      this.notificationState.notifications.length === 0
    ) {
      listContainer.innerHTML = `
                <div class="notification-empty">
                    <i class="fa fa-bell-slash"></i>
                    <h5>Không có thông báo</h5>
                    <p>Bạn sẽ nhận được thông báo khi có tin nhắn mới hoặc cập nhật hệ thống.</p>
                </div>
            `;
      return;
    }

    listContainer.innerHTML = this.notificationState.notifications
      .map(
        (notification) => `
            <div class="notification-item ${
              notification.is_read ? "" : "unread"
            }" 
                 data-id="${notification.id}"
                 onclick="sbotchatWidget.markNotificationRead(${
                   notification.id
                 })">
                <div class="notification-icon type-${
                  notification.notification_type
                }">
                    ${this.getNotificationIcon(notification.notification_type)}
                    ${
                      notification.created_by_ai
                        ? '<div class="notification-ai-badge">AI</div>'
                        : ""
                    }
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${
                      notification.message
                    }</div>
                    <div class="notification-time">${this.formatNotificationTime(
                      notification.sent_date || notification.create_date
                    )}</div>
                </div>
            </div>
        `
      )
      .join("");
  }

  /**
   * Format notification timestamp
   */
  formatNotificationTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) return "Vừa xong";
    if (diff < 3600000) return `${Math.floor(diff / 60000)} phút trước`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)} giờ trước`;
    return date.toLocaleDateString("vi-VN");
  }

  /**
   * Mark notification as read
   */
  async markNotificationRead(notificationId) {
    try {
      const response = await this.rpcCall(
        `/sbotchat/notification/${notificationId}/mark_read`,
        {}
      );

      if (response.success) {
        // Update local state
        const notification = this.notificationState.notifications.find(
          (n) => n.id === notificationId
        );
        if (notification) {
          notification.is_read = true;
          this.notificationState.unreadCount--;
          this.updateNotificationUI();
          this.renderNotificationList();
        }
      }
    } catch (error) {
      console.error("❌ Failed to mark notification as read:", error);
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllNotificationsRead() {
    try {
      const unreadNotifications = this.notificationState.notifications.filter(
        (n) => !n.is_read
      );

      for (const notification of unreadNotifications) {
        await this.markNotificationRead(notification.id);
      }

      this.showNotification("Đã đánh dấu tất cả thông báo đã đọc", "success");
    } catch (error) {
      console.error("❌ Failed to mark all notifications as read:", error);
    }
  }

  /**
   * Update notification UI
   */
  updateNotificationUI() {
    if (this.notificationBtn) {
      const badge = this.notificationBtn.querySelector(".notification-badge");
      if (badge) {
        badge.textContent = this.notificationState.unreadCount || 0;
        if (this.notificationState.unreadCount > 0) {
          badge.classList.remove("hidden");
        } else {
          badge.classList.add("hidden");
        }
      }
    }
  }

  /**
   * Test notification system
   */
  async testNotificationSystem() {
    try {
      const response = await this.rpcCall("/sbotchat/notification/test", {});

      if (response.success) {
        this.showNotification("Test notification sent successfully", "success");
      } else {
        this.showNotification("Failed to send test notification", "danger");
      }
    } catch (error) {
      console.error("❌ Failed to test notification:", error);
      this.showNotification("Error testing notification system", "danger");
    }
  }

  /**
   * Get current user ID
   */
  getCurrentUserId() {
    // Try multiple sources for user ID
    if (this.user && this.user.userId) {
      return this.user.userId;
    }

    if (
      window.odoo &&
      window.odoo.session_info &&
      window.odoo.session_info.uid
    ) {
      return window.odoo.session_info.uid;
    }

    // Fallback to default
    return 1;
  }

  /**
   * Cleanup notification system
   */
  cleanupNotificationSystem() {
    if (this.notificationPollingInterval) {
      clearInterval(this.notificationPollingInterval);
    }

    if (this.notificationPanel) {
      this.notificationPanel.remove();
    }

    if (this.notificationBtn) {
      this.notificationBtn.remove();
    }
  }

  /**
   * 🚀 PHASE 1: Initialize Redis Cache System
   */
  async initializeRedisCache() {
    try {
      console.log("🚀 Initializing Redis Cache System...");

      // Check Redis connection status
      await this.checkRedisStatus();

      // Load cache statistics
      await this.loadCacheStats();

      // Setup cache status monitoring
      this.setupCacheMonitoring();

      console.log("✅ Redis Cache System initialized");
    } catch (error) {
      console.error("❌ Redis initialization failed:", error);
      this.state.redisStatus = "error";
    }
  }

  /**
   * Check Redis connection status with robust error handling
   */
  async checkRedisStatus() {
    try {
      // Set checking status to show loading indicator
      this.state.redisStatus = "checking";
      this.render();

      const response = await this.rpcCall("/sbotchat/redis/status");

      // Robust response validation
      if (response && typeof response === "object") {
        if (response.success === true && response.is_connected === true) {
          this.state.redisStatus = "online";
          console.log(`✅ Redis Status: ONLINE`, response);
        } else if (
          response.success === true &&
          response.is_connected === false
        ) {
          this.state.redisStatus = "offline";
          console.log(`❌ Redis Status: OFFLINE`, response);
        } else if (response.success === false) {
          this.state.redisStatus = "error";
          console.warn(
            `⚠️ Redis Status: ERROR - ${response.error || "Unknown error"}`
          );
        } else {
          // Fallback: check if detection_method exists (indicates successful detection)
          if (
            response.detection_method &&
            response.detection_method !== "none"
          ) {
            this.state.redisStatus = "online";
            console.log(
              `✅ Redis Status: ONLINE (via ${response.detection_method})`
            );
          } else {
            this.state.redisStatus = "offline";
            console.warn(`❌ Redis Status: OFFLINE (no detection method)`);
          }
        }
      } else {
        this.state.redisStatus = "error";
        console.error(`❌ Redis Status: Invalid response format`, response);
      }

      // Force UI update
      this.render();
    } catch (error) {
      console.error("❌ Redis status check failed:", error);

      // Don't immediately set to offline - try fallback check
      try {
        // Fallback: Try a simple Redis ping via direct endpoint
        const fallbackResponse = await this.rpcCall("/sbotchat/cache/health");
        if (
          fallbackResponse &&
          fallbackResponse.success &&
          fallbackResponse.status === "healthy"
        ) {
          this.state.redisStatus = "online";
          console.log(`✅ Redis Status: ONLINE (via fallback health check)`);
        } else {
          this.state.redisStatus = "offline";
          console.warn(`❌ Redis Status: OFFLINE (fallback also failed)`);
        }
      } catch (fallbackError) {
        this.state.redisStatus = "error";
        console.error(
          `❌ Redis Status: ERROR (both primary and fallback failed)`,
          error,
          fallbackError
        );
      }

      this.render();
    }
  }

  async loadTokenStatistics() {
    try {
      const response = await this.rpcCall("/sbotchat/token/statistics");
      if (response.success) {
        this.state.tokenStats = response.statistics;
        console.log("📊 Token Statistics:", response.statistics);

        // Check for warnings
        const warningData = response.statistics.warning_data;
        if (warningData && warningData.warning_level !== "none") {
          console.warn("⚠️ Token Warning:", warningData.message);

          // Show warning notification for high/critical levels
          if (["high", "critical"].includes(warningData.warning_level)) {
            this.showTokenWarning(warningData);
          }
        }
      }
    } catch (error) {
      console.warn("⚠️ Failed to load token statistics:", error);
    }
  }

  showTokenWarning(warningData) {
    try {
      // Create warning notification
      const warningClass =
        warningData.warning_level === "critical"
          ? "token-warning-critical"
          : "token-warning-high";
      const warningIcon =
        warningData.warning_level === "critical" ? "🚨" : "⚠️";

      // Add to chat as system message
      const warningMessage = {
        role: "system",
        content: `${warningIcon} ${warningData.message}`,
        timestamp: new Date().toISOString(),
        isWarning: true,
        warningLevel: warningData.warning_level,
      };

      this.addMessageToChat(warningMessage);
      console.log("⚠️ Token warning displayed:", warningData.message);
    } catch (error) {
      console.error("Failed to show token warning:", error);
    }
  }

  /**
   * Load cache statistics
   */
  async loadCacheStats() {
    try {
      const response = await this.rpcCall("/sbotchat/cache/stats");
      this.state.cacheStats = {
        hits: response.hits || 0,
        misses: response.misses || 0,
        hitRate: response.hit_rate || 0,
        tokensSaved: response.tokens_saved || 0,
      };
      console.log("📊 Cache stats loaded:", this.state.cacheStats);
    } catch (error) {
      console.warn("⚠️ Cache stats load failed:", error);
    }
  }

  /**
   * Setup cache monitoring
   */
  setupCacheMonitoring() {
    // Refresh cache stats every 30 seconds
    setInterval(() => {
      this.loadCacheStats();
    }, 30000);

    // Check Redis status every 60 seconds
    setInterval(() => {
      this.checkRedisStatus();
    }, 60000);

    // Load token statistics every 5 minutes
    setInterval(() => {
      this.loadTokenStatistics();
    }, 300000);
  }

  /**
   * Toggle cache enabled/disabled
   */
  async toggleCache() {
    try {
      this.state.cacheEnabled = !this.state.cacheEnabled;
      this.state.config.cache_responses = this.state.cacheEnabled;

      // Save to backend
      await this.rpcCall("/sbotchat/cache/toggle", {
        enabled: this.state.cacheEnabled,
      });

      this.showNotification(
        `Cache ${this.state.cacheEnabled ? "enabled" : "disabled"}`,
        this.state.cacheEnabled ? "success" : "warning"
      );
    } catch (error) {
      console.error("❌ Cache toggle failed:", error);
      this.showNotification("Failed to toggle cache", "error");
    }
  }

  /**
   * Show cache statistics panel
   */
  toggleCacheStats() {
    this.state.showCacheStats = !this.state.showCacheStats;
    if (this.state.showCacheStats) {
      this.loadCacheStats();
    }
  }

  /**
   * Clear all cache
   */
  async clearAllCache() {
    try {
      const confirmed = confirm(
        "Are you sure you want to clear all cache? This will temporarily impact performance."
      );
      if (!confirmed) return;

      await this.rpcCall("/sbotchat/cache/clear");

      // Reset stats
      this.state.cacheStats = {
        hits: 0,
        misses: 0,
        hitRate: 0,
        tokensSaved: 0,
      };

      this.showNotification("All cache cleared successfully", "success");
    } catch (error) {
      console.error("❌ Cache clear failed:", error);
      this.showNotification("Failed to clear cache", "error");
    }
  }

  /**
   * Get Redis status indicator
   */
  getRedisStatusIndicator() {
    const status = this.state.redisStatus;
    const indicators = {
      checking: "🔄",
      online: "🟢",
      offline: "🔴",
      error: "⚠️",
    };
    return indicators[status] || "❓";
  }

  /**
   * Get cache hit rate color
   */
  getCacheHitRateColor() {
    const hitRate = this.state.cacheStats.hitRate;
    if (hitRate >= 80) return "#28a745"; // Green
    if (hitRate >= 60) return "#ffc107"; // Yellow
    return "#dc3545"; // Red
  }
}

// Register the component
registry.category("actions").add("sbotchat.interface", SbotchatWidget);

// Global widget instance for floating button
window.SbotchatWidget = SbotchatWidget;

export default SbotchatWidget;
