<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Record rule for sbotchat.pending.request -->
        <record id="sbotchat_pending_request_rule" model="ir.rule">
            <field name="name">SbotChat Pending Request: Own Records Only</field>
            <field name="model_id" ref="model_sbotchat_pending_request"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- Record rule for sbotchat.conversation -->
        <record id="sbotchat_conversation_rule" model="ir.rule">
            <field name="name">SbotChat Conversation: Own Records Only</field>
            <field name="model_id" ref="model_sbotchat_conversation"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- Record rule for sbotchat.message -->
        <record id="sbotchat_message_rule" model="ir.rule">
            <field name="name">SbotChat Message: Own Conversation Records Only</field>
            <field name="model_id" ref="model_sbotchat_message"/>
            <field name="domain_force">[('conversation_id.user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- Record rule for sbotchat.config -->
        <record id="sbotchat_config_rule" model="ir.rule">
            <field name="name">SbotChat Config: Own Records Only</field>
            <field name="model_id" ref="model_sbotchat_config"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
    </data>
</odoo> 