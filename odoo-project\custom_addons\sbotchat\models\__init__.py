# -*- coding: utf-8 -*-

from . import sbotchat_config
from . import sbotchat_conversation 
from . import sbotchat_pending_request

# imports of deleted legacy helpers removed
# Register scheduled maintenance model
from . import sbotchat_scheduler
# Advanced reasoning and validation engines
from . import sbotchat_reasoning
from . import sbotchat_validation
from . import sbotchat_performance
# Notification system
from . import sbotchat_notification
from . import enhanced_memory
from . import multi_agent_system
from . import security_framework
from . import advanced_tools
from . import universal_workflow_engine
from . import redis_service
# 🚀 PHASE 1: Redis Infrastructure
from . import redis_manager
from . import cache_service
# Vector Store OpenAI integration
from . import vector_store_file
from . import vector_store_manager
from . import file_upload_manager
from . import vector_store_tool

# Auto-initialize Redis shared memory when module loads
import logging
_logger = logging.getLogger(__name__)

def init_redis_on_module_load():
    """Initialize Redis shared memory when SBotChat module loads"""
    try:
        from ..lib import init_shared_memory
        
        # Try to initialize with default settings
        success = init_shared_memory()
        if success:
            _logger.info("✅ Redis Shared Memory auto-initialized on module load")
        else:
            _logger.warning("⚠️ Redis Shared Memory initialization failed - will fallback to local memory")
            
    except Exception as e:
        _logger.warning(f"⚠️ Redis auto-initialization failed: {e} - will fallback to local memory")

# Auto-initialize Redis when module is imported
init_redis_on_module_load()